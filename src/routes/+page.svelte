<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { onMount, onDestroy } from "svelte";

  // Import our refactored components
  import Header from "$lib/components/Header.svelte";
  import ComponentLibrary from "$lib/components/ComponentLibrary.svelte";
  import ShadowDOMPreview from "$lib/components/ShadowDOMPreview.svelte";
  import EnhancedCanvas from "$lib/components/EnhancedCanvas.svelte";
  import PropertyPanel from "$lib/components/PropertyPanel.svelte";
  import CommandPalette from "$lib/components/CommandPalette.svelte";

  // Import stores and utilities
  import {
    selectedComponent,
    selectedElement,
    updateComponentHTML
  } from "$lib/components/ComponentStore";
  import type { Component } from "$lib/components/ComponentStore";
  import { theme, applyTheme } from "$lib/stores/ThemeStore";
  import { currentProject } from "$lib/stores/ProjectStore";
  import { handleKeyboardEvent } from "$lib/stores/KeyboardStore";
  import type { CanvasComponent } from "$lib/stores/ProjectStore";

  // Layout state
  let leftSidebarCollapsed = false;
  let rightSidebarCollapsed = false;
  let leftSidebarWidth = 280;
  let rightSidebarWidth = 320;
  let useEnhancedCanvas = true; // Toggle between old and new canvas

  // Function to select a component
  function selectComponent(component: Component) {
    // Set the selected component
    selectedComponent.set(component);

    // Reset the selected element
    selectedElement.set(null);

    // Ensure the component HTML is generated from its properties
    updateComponentHTML(component);
  }

  // Reference to preview components
  let shadowDOMPreview: ShadowDOMPreview;
  let enhancedCanvas: EnhancedCanvas;

  // Function to update a specific component in the preview
  function updateComponent(component: Component) {
    if (shadowDOMPreview) {
      shadowDOMPreview.updateComponent(component);
    }
  }

  // Handle canvas component selection
  function handleCanvasComponentSelect(component: CanvasComponent) {
    console.log('Selected canvas component:', component);
    // You could update the property panel to show canvas component properties
  }

  // Toggle sidebar functions
  function toggleLeftSidebar() {
    leftSidebarCollapsed = !leftSidebarCollapsed;
  }

  function toggleRightSidebar() {
    rightSidebarCollapsed = !rightSidebarCollapsed;
  }

  // Resize functionality
  let isResizingLeft = false;
  let isResizingRight = false;
  let startX = 0;
  let startWidth = 0;

  function handleLeftResizeStart(e: MouseEvent) {
    if (leftSidebarCollapsed) return;
    isResizingLeft = true;
    startX = e.clientX;
    startWidth = leftSidebarWidth;
    e.preventDefault();
  }

  function handleRightResizeStart(e: MouseEvent) {
    if (rightSidebarCollapsed) return;
    isResizingRight = true;
    startX = e.clientX;
    startWidth = rightSidebarWidth;
    e.preventDefault();
  }

  function handleMouseMove(e: MouseEvent) {
    if (isResizingLeft) {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(200, Math.min(500, startWidth + deltaX));
      leftSidebarWidth = newWidth;
    } else if (isResizingRight) {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(250, Math.min(600, startWidth - deltaX));
      rightSidebarWidth = newWidth;
    }
  }

  function handleMouseUp() {
    isResizingLeft = false;
    isResizingRight = false;
  }

  // Apply theme when it changes
  $: if ($theme) {
    applyTheme($theme);
  }

  // Keyboard shortcuts
  function handleKeydown(e: KeyboardEvent) {
    // Handle global keyboard shortcuts first
    const handled = handleKeyboardEvent(e);

    if (!handled) {
      // Handle local shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'b':
            e.preventDefault();
            toggleLeftSidebar();
            break;
          case 'i':
            e.preventDefault();
            toggleRightSidebar();
            break;
          case 'e':
            e.preventDefault();
            useEnhancedCanvas = !useEnhancedCanvas;
            break;
        }
      }
    }
  }

  onMount(() => {
    // Apply theme on mount
    applyTheme($theme);

    // Add global mouse event listeners for resizing
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Add keyboard shortcuts
    document.addEventListener('keydown', handleKeydown);
  });

  onDestroy(() => {
    // Clean up event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('keydown', handleKeydown);
  });
</script>

<div class="app-container h-screen w-screen flex flex-col theme-{$theme}">
  <!-- Top Menu Bar -->
  <Header />

  <!-- Main Content Area -->
  <div class="flex flex-1 overflow-hidden relative">
    <!-- Left Sidebar - Component Library -->
    <div
      class="left-sidebar relative transition-all duration-200 ease-in-out"
      class:collapsed={leftSidebarCollapsed}
      style="width: {leftSidebarCollapsed ? '0' : leftSidebarWidth + 'px'}; min-width: {leftSidebarCollapsed ? '0' : '200px'}; max-width: 500px;"
    >
      <ComponentLibrary
        onSelectComponent={selectComponent}
        collapsed={leftSidebarCollapsed}
      />

      <!-- Resize Handle -->
      {#if !leftSidebarCollapsed}
        <div
          class="resize-handle resize-handle-right"
          role="separator"
          aria-label="Resize component library"
          onmousedown={handleLeftResizeStart}
        ></div>
      {/if}

      <!-- Toggle Button -->
      <button
        class="sidebar-toggle sidebar-toggle-left"
        class:collapsed={leftSidebarCollapsed}
        onclick={toggleLeftSidebar}
        title={leftSidebarCollapsed ? 'Show Component Library (Ctrl+B)' : 'Hide Component Library (Ctrl+B)'}
      >
        <span class="toggle-icon">
          {leftSidebarCollapsed ? '▶' : '◀'}
        </span>
      </button>
    </div>

    <!-- Central Canvas/Preview Area -->
    <div class="flex-1 relative">
      <!-- Canvas Toggle Button -->
      <div class="canvas-toggle">
        <button
          class="toggle-btn"
          class:active={useEnhancedCanvas}
          onclick={() => useEnhancedCanvas = true}
          title="Enhanced Canvas (Ctrl+E)"
        >
          Enhanced
        </button>
        <button
          class="toggle-btn"
          class:active={!useEnhancedCanvas}
          onclick={() => useEnhancedCanvas = false}
          title="Preview Mode"
        >
          Preview
        </button>
      </div>

      {#if useEnhancedCanvas}
        <EnhancedCanvas
          bind:this={enhancedCanvas}
          onComponentSelect={handleCanvasComponentSelect}
        />
      {:else}
        <ShadowDOMPreview
          bind:this={shadowDOMPreview}
          onSelectComponent={selectComponent}
        />
      {/if}
    </div>

    <!-- Right Sidebar - Properties Panel -->
    <div
      class="right-sidebar relative transition-all duration-200 ease-in-out"
      class:collapsed={rightSidebarCollapsed}
      style="width: {rightSidebarCollapsed ? '0' : rightSidebarWidth + 'px'}; min-width: {rightSidebarCollapsed ? '0' : '250px'}; max-width: 600px;"
    >
      <!-- Toggle Button -->
      <button
        class="sidebar-toggle sidebar-toggle-right"
        class:collapsed={rightSidebarCollapsed}
        onclick={toggleRightSidebar}
        title={rightSidebarCollapsed ? 'Show Properties Panel (Ctrl+I)' : 'Hide Properties Panel (Ctrl+I)'}
      >
        <span class="toggle-icon">
          {rightSidebarCollapsed ? '◀' : '▶'}
        </span>
      </button>

      <!-- Resize Handle -->
      {#if !rightSidebarCollapsed}
        <div
          class="resize-handle resize-handle-left"
          role="separator"
          aria-label="Resize properties panel"
          onmousedown={handleRightResizeStart}
        ></div>
      {/if}

      <PropertyPanel
        onUpdateComponent={updateComponent}
        collapsed={rightSidebarCollapsed}
      />
    </div>
  </div>
</div>

<!-- Global Components -->
<CommandPalette />

<style>
  .left-sidebar, .right-sidebar {
    overflow: hidden;
    position: relative;
  }

  .left-sidebar.collapsed, .right-sidebar.collapsed {
    overflow: visible;
  }

  /* Resize Handles */
  .resize-handle {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 8px;
    cursor: ew-resize;
    user-select: none;
    z-index: 10;
    background: linear-gradient(90deg, transparent 45%, #cbd5e1 45%, #cbd5e1 55%, transparent 55%);
    background-size: 100% 20px;
    background-position: center;
    background-repeat: no-repeat;
    transition: background 0.2s ease;
  }

  .resize-handle:hover {
    background: linear-gradient(90deg, transparent 40%, #94a3b8 40%, #94a3b8 60%, transparent 60%);
  }

  .resize-handle-right {
    right: 0;
  }

  .resize-handle-left {
    left: 0;
  }

  /* Sidebar Toggle Buttons */
  .sidebar-toggle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 48px;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    z-index: 20;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  .sidebar-toggle:hover {
    background: var(--color-hover);
    transform: translateY(-50%) scale(1.05);
  }

  .sidebar-toggle-left {
    right: -24px;
  }

  .sidebar-toggle-left.collapsed {
    left: 0;
    border-radius: 0 8px 8px 0;
  }

  .sidebar-toggle-right {
    left: -24px;
  }

  .sidebar-toggle-right.collapsed {
    right: 0;
    border-radius: 8px 0 0 8px;
  }

  .toggle-icon {
    font-size: 12px;
    color: var(--color-secondary);
    transition: color 0.2s ease;
  }

  .sidebar-toggle:hover .toggle-icon {
    color: var(--color-primary);
  }

  /* Canvas Toggle Styles */
  .canvas-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    display: flex;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .toggle-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    color: var(--color-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-right: 1px solid var(--color-border);
  }

  .toggle-btn:last-child {
    border-right: none;
  }

  .toggle-btn:hover {
    background: var(--color-hover);
    color: var(--color-text);
  }

  .toggle-btn.active {
    background: var(--color-primary);
    color: white;
  }

  /* Prevent text selection during resize */
  .resize-handle:active,
  .resize-handle:active ~ * {
    user-select: none;
    pointer-events: none;
  }

  /* Smooth transitions */
  .left-sidebar, .right-sidebar {
    transition: width 0.2s ease-in-out;
  }

  /* Responsive behavior */
  @media (max-width: 1024px) {
    .left-sidebar, .right-sidebar {
      min-width: 0 !important;
    }

    .sidebar-toggle {
      width: 20px;
      height: 40px;
    }
  }

  @media (max-width: 768px) {
    .left-sidebar:not(.collapsed) {
      width: 240px !important;
      max-width: 240px !important;
    }

    .right-sidebar:not(.collapsed) {
      width: 280px !important;
      max-width: 280px !important;
    }
  }
</style>
