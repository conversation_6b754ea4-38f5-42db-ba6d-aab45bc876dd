import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import type { Component } from '../components/ComponentStore';

// Project interface
export interface Project {
  id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt: number;
  version: string;
  canvasComponents: CanvasComponent[];
  settings: ProjectSettings;
}

// Canvas component interface
export interface CanvasComponent {
  id: string;
  componentId: number;
  html: string;
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  parentId?: string;
  children: string[];
  zIndex: number;
  locked: boolean;
  visible: boolean;
}

// Project settings interface
export interface ProjectSettings {
  gridEnabled: boolean;
  gridSize: number;
  snapToGrid: boolean;
  showGuides: boolean;
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  exportOptions: {
    includeBootstrapCDN: boolean;
    separateCSS: boolean;
    minifyOutput: boolean;
    includeComments: boolean;
  };
}

// Project version for history
export interface ProjectVersion {
  id: string;
  projectId: string;
  timestamp: number;
  description: string;
  thumbnail?: string;
  data: Project;
}

// Default project settings
const defaultSettings: ProjectSettings = {
  gridEnabled: true,
  gridSize: 8,
  snapToGrid: true,
  showGuides: true,
  autoSave: true,
  autoSaveInterval: 30,
  exportOptions: {
    includeBootstrapCDN: true,
    separateCSS: false,
    minifyOutput: false,
    includeComments: true
  }
};

// Create default project
function createDefaultProject(): Project {
  return {
    id: generateId(),
    name: 'Untitled Project',
    description: '',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    version: '1.0.0',
    canvasComponents: [],
    settings: { ...defaultSettings }
  };
}

// Generate unique ID
function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Current project store
export const currentProject = writable<Project>(createDefaultProject());

// Project versions store (for history)
export const projectVersions = writable<ProjectVersion[]>([]);

// Auto-save timer
let autoSaveTimer: NodeJS.Timeout | null = null;

// Load project from localStorage
function loadProject(): Project {
  if (!browser) return createDefaultProject();
  
  try {
    const stored = localStorage.getItem('currentProject');
    if (stored) {
      const project = JSON.parse(stored);
      // Ensure all required properties exist
      return {
        ...createDefaultProject(),
        ...project,
        settings: { ...defaultSettings, ...project.settings }
      };
    }
  } catch (error) {
    console.warn('Failed to load project:', error);
  }
  
  return createDefaultProject();
}

// Save project to localStorage
function saveProject(project: Project): void {
  if (!browser) return;
  
  try {
    project.updatedAt = Date.now();
    localStorage.setItem('currentProject', JSON.stringify(project));
  } catch (error) {
    console.warn('Failed to save project:', error);
  }
}

// Load project versions from localStorage
function loadProjectVersions(): ProjectVersion[] {
  if (!browser) return [];
  
  try {
    const stored = localStorage.getItem('projectVersions');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn('Failed to load project versions:', error);
  }
  
  return [];
}

// Save project versions to localStorage
function saveProjectVersions(versions: ProjectVersion[]): void {
  if (!browser) return;
  
  try {
    // Keep only the last 10 versions
    const limitedVersions = versions.slice(-10);
    localStorage.setItem('projectVersions', JSON.stringify(limitedVersions));
  } catch (error) {
    console.warn('Failed to save project versions:', error);
  }
}

// Create a new project version
export function createProjectVersion(project: Project, description: string = 'Auto-save'): void {
  const version: ProjectVersion = {
    id: generateId(),
    projectId: project.id,
    timestamp: Date.now(),
    description,
    data: JSON.parse(JSON.stringify(project)) // Deep clone
  };
  
  projectVersions.update(versions => {
    const newVersions = [...versions, version];
    saveProjectVersions(newVersions);
    return newVersions;
  });
}

// Update current project
export function updateProject(updates: Partial<Project>): void {
  currentProject.update(project => {
    const updatedProject = { ...project, ...updates, updatedAt: Date.now() };
    saveProject(updatedProject);
    return updatedProject;
  });
}

// Start auto-save
export function startAutoSave(): void {
  currentProject.subscribe(project => {
    if (project.settings.autoSave && browser) {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
      
      autoSaveTimer = setTimeout(() => {
        saveProject(project);
        createProjectVersion(project, 'Auto-save');
      }, project.settings.autoSaveInterval * 1000);
    }
  });
}

// Stop auto-save
export function stopAutoSave(): void {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = null;
  }
}

// Export project as JSON
export function exportProjectAsJSON(project: Project): string {
  return JSON.stringify(project, null, 2);
}

// Import project from JSON
export function importProjectFromJSON(jsonString: string): Project {
  try {
    const imported = JSON.parse(jsonString);
    return {
      ...createDefaultProject(),
      ...imported,
      id: generateId(), // Generate new ID for imported project
      createdAt: Date.now(),
      updatedAt: Date.now(),
      settings: { ...defaultSettings, ...imported.settings }
    };
  } catch (error) {
    throw new Error('Invalid project JSON format');
  }
}

// Initialize project store
if (browser) {
  currentProject.set(loadProject());
  projectVersions.set(loadProjectVersions());
  startAutoSave();
}

// Derived store for project status
export const projectStatus = derived(
  [currentProject, projectVersions],
  ([project, versions]) => ({
    hasUnsavedChanges: versions.length === 0 || 
      (versions[versions.length - 1]?.timestamp || 0) < project.updatedAt,
    lastSaved: versions[versions.length - 1]?.timestamp || project.createdAt,
    versionCount: versions.length
  })
);
