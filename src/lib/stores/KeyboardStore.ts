import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { executeCommand, undoCommand, redoCommand } from './CommandStore';
import type { Component } from '../components/ComponentStore';

// Keyboard shortcut interface
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: () => void;
}

// Clipboard store for copy/paste
export const clipboard = writable<Component | null>(null);

// Selected components for multi-select
export const selectedComponents = writable<Component[]>([]);

// Keyboard shortcuts registry
const shortcuts: KeyboardShortcut[] = [];

// Register a keyboard shortcut
export function registerShortcut(shortcut: KeyboardShortcut): void {
  shortcuts.push(shortcut);
}

// Unregister a keyboard shortcut
export function unregisterShortcut(key: string, modifiers?: { ctrlKey?: boolean; shiftKey?: boolean; altKey?: boolean; metaKey?: boolean }): void {
  const index = shortcuts.findIndex(s => 
    s.key === key && 
    s.ctrlKey === modifiers?.ctrlKey &&
    s.shiftKey === modifiers?.shiftKey &&
    s.altKey === modifiers?.altKey &&
    s.metaKey === modifiers?.metaKey
  );
  if (index !== -1) {
    shortcuts.splice(index, 1);
  }
}

// Handle keyboard events
export function handleKeyboardEvent(event: KeyboardEvent): boolean {
  const shortcut = shortcuts.find(s => 
    s.key.toLowerCase() === event.key.toLowerCase() &&
    !!s.ctrlKey === event.ctrlKey &&
    !!s.shiftKey === event.shiftKey &&
    !!s.altKey === event.altKey &&
    !!s.metaKey === event.metaKey
  );

  if (shortcut) {
    event.preventDefault();
    shortcut.action();
    return true;
  }

  return false;
}

// Copy component to clipboard
export function copyComponent(component: Component): void {
  if (!component) return;
  
  // Create a deep copy of the component
  const componentCopy: Component = JSON.parse(JSON.stringify(component));
  clipboard.set(componentCopy);
  
  // Show feedback (could be enhanced with a toast notification)
  console.log(`Copied ${component.name} to clipboard`);
}

// Paste component from clipboard
export function pasteComponent(): Component | null {
  let clipboardComponent: Component | null = null;
  
  clipboard.subscribe(component => {
    clipboardComponent = component;
  })();
  
  if (!clipboardComponent) return null;
  
  // Create a new instance with a unique ID
  const pastedComponent: Component = {
    ...clipboardComponent,
    id: Date.now(), // Generate new ID
    properties: {
      ...clipboardComponent.properties,
      id: `${clipboardComponent.properties.id}-copy-${Date.now()}`
    }
  };
  
  return pastedComponent;
}

// Delete selected components
export function deleteSelectedComponents(): void {
  selectedComponents.update(components => {
    if (components.length > 0) {
      // Create delete command for undo/redo
      const deleteCommand = {
        id: `delete-${Date.now()}`,
        type: 'delete',
        timestamp: Date.now(),
        description: `Delete ${components.length} component(s)`,
        execute: () => {
          // Remove components from canvas
          components.forEach(component => {
            // This would need to be integrated with the canvas system
            console.log(`Deleting component: ${component.name}`);
          });
        },
        undo: () => {
          // Restore components to canvas
          components.forEach(component => {
            console.log(`Restoring component: ${component.name}`);
          });
        }
      };
      
      executeCommand(deleteCommand);
    }
    return [];
  });
}

// Select all components
export function selectAllComponents(allComponents: Component[]): void {
  selectedComponents.set([...allComponents]);
}

// Clear selection
export function clearSelection(): void {
  selectedComponents.set([]);
}

// Toggle component selection
export function toggleComponentSelection(component: Component): void {
  selectedComponents.update(selected => {
    const index = selected.findIndex(c => c.id === component.id);
    if (index !== -1) {
      // Remove from selection
      return selected.filter(c => c.id !== component.id);
    } else {
      // Add to selection
      return [...selected, component];
    }
  });
}

// Default keyboard shortcuts
export function initializeDefaultShortcuts(): void {
  // Undo
  registerShortcut({
    key: 'z',
    ctrlKey: true,
    description: 'Undo',
    action: () => {
      undoCommand();
    }
  });

  // Redo
  registerShortcut({
    key: 'y',
    ctrlKey: true,
    description: 'Redo',
    action: () => {
      redoCommand();
    }
  });

  // Redo (alternative)
  registerShortcut({
    key: 'z',
    ctrlKey: true,
    shiftKey: true,
    description: 'Redo',
    action: () => {
      redoCommand();
    }
  });

  // Copy
  registerShortcut({
    key: 'c',
    ctrlKey: true,
    description: 'Copy',
    action: () => {
      selectedComponents.subscribe(components => {
        if (components.length === 1) {
          copyComponent(components[0]);
        }
      })();
    }
  });

  // Paste
  registerShortcut({
    key: 'v',
    ctrlKey: true,
    description: 'Paste',
    action: () => {
      const pastedComponent = pasteComponent();
      if (pastedComponent) {
        // This would need to be integrated with the canvas system
        console.log('Pasted component:', pastedComponent.name);
      }
    }
  });

  // Delete
  registerShortcut({
    key: 'Delete',
    description: 'Delete',
    action: () => {
      deleteSelectedComponents();
    }
  });

  // Delete (alternative)
  registerShortcut({
    key: 'Backspace',
    description: 'Delete',
    action: () => {
      deleteSelectedComponents();
    }
  });

  // Select All
  registerShortcut({
    key: 'a',
    ctrlKey: true,
    description: 'Select All',
    action: () => {
      // This would need access to all components
      console.log('Select all components');
    }
  });

  // Escape (clear selection)
  registerShortcut({
    key: 'Escape',
    description: 'Clear Selection',
    action: () => {
      clearSelection();
    }
  });

  // Save
  registerShortcut({
    key: 's',
    ctrlKey: true,
    description: 'Save',
    action: () => {
      // This would trigger manual save
      console.log('Manual save triggered');
    }
  });
}

// Get all registered shortcuts for display
export function getAllShortcuts(): KeyboardShortcut[] {
  return [...shortcuts];
}

// Format shortcut for display
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey || shortcut.metaKey) {
    parts.push(navigator.platform.includes('Mac') ? '⌘' : 'Ctrl');
  }
  if (shortcut.shiftKey) {
    parts.push('Shift');
  }
  if (shortcut.altKey) {
    parts.push(navigator.platform.includes('Mac') ? '⌥' : 'Alt');
  }
  
  parts.push(shortcut.key.toUpperCase());
  
  return parts.join(' + ');
}

// Initialize keyboard shortcuts when in browser
if (browser) {
  initializeDefaultShortcuts();
}
