import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Command interface for undo/redo system
export interface Command {
  id: string;
  type: string;
  timestamp: number;
  execute: () => void;
  undo: () => void;
  description: string;
}

// Command history store
interface CommandHistory {
  commands: Command[];
  currentIndex: number;
  maxCommands: number;
}

const initialHistory: CommandHistory = {
  commands: [],
  currentIndex: -1,
  maxCommands: 20
};

// Create the command history store
export const commandHistory = writable<CommandHistory>(initialHistory);

// Load command history from sessionStorage
function loadCommandHistory(): CommandHistory {
  if (!browser) return initialHistory;
  
  try {
    const stored = sessionStorage.getItem('commandHistory');
    if (stored) {
      const parsed = JSON.parse(stored);
      // Don't restore the actual command functions, just the metadata
      return {
        commands: [],
        currentIndex: -1,
        maxCommands: parsed.maxCommands || 20
      };
    }
  } catch (error) {
    console.warn('Failed to load command history:', error);
  }
  
  return initialHistory;
}

// Save command history to sessionStorage
function saveCommandHistory(history: CommandHistory): void {
  if (!browser) return;
  
  try {
    // Only save metadata, not the actual functions
    const toSave = {
      commandCount: history.commands.length,
      currentIndex: history.currentIndex,
      maxCommands: history.maxCommands,
      lastCommand: history.commands[history.currentIndex]?.description || null
    };
    sessionStorage.setItem('commandHistory', JSON.stringify(toSave));
  } catch (error) {
    console.warn('Failed to save command history:', error);
  }
}

// Execute a command and add it to history
export function executeCommand(command: Command): void {
  commandHistory.update(history => {
    // Execute the command
    command.execute();
    
    // Remove any commands after current index (when undoing and then executing new command)
    const newCommands = history.commands.slice(0, history.currentIndex + 1);
    
    // Add the new command
    newCommands.push(command);
    
    // Limit the number of commands
    if (newCommands.length > history.maxCommands) {
      newCommands.shift();
    } else {
      history.currentIndex++;
    }
    
    const newHistory = {
      ...history,
      commands: newCommands,
      currentIndex: Math.min(history.currentIndex, newCommands.length - 1)
    };
    
    saveCommandHistory(newHistory);
    return newHistory;
  });
}

// Undo the last command
export function undoCommand(): boolean {
  let success = false;
  
  commandHistory.update(history => {
    if (history.currentIndex >= 0) {
      const command = history.commands[history.currentIndex];
      command.undo();
      success = true;
      
      const newHistory = {
        ...history,
        currentIndex: history.currentIndex - 1
      };
      
      saveCommandHistory(newHistory);
      return newHistory;
    }
    return history;
  });
  
  return success;
}

// Redo the next command
export function redoCommand(): boolean {
  let success = false;
  
  commandHistory.update(history => {
    if (history.currentIndex < history.commands.length - 1) {
      const nextIndex = history.currentIndex + 1;
      const command = history.commands[nextIndex];
      command.execute();
      success = true;
      
      const newHistory = {
        ...history,
        currentIndex: nextIndex
      };
      
      saveCommandHistory(newHistory);
      return newHistory;
    }
    return history;
  });
  
  return success;
}

// Check if undo is available
export function canUndo(): boolean {
  const history = get(commandHistory);
  return history.currentIndex >= 0;
}

// Check if redo is available
export function canRedo(): boolean {
  const history = get(commandHistory);
  return history.currentIndex < history.commands.length - 1;
}

// Clear command history
export function clearCommandHistory(): void {
  commandHistory.set(initialHistory);
  if (browser) {
    sessionStorage.removeItem('commandHistory');
  }
}

// Initialize command history on load
if (browser) {
  commandHistory.set(loadCommandHistory());
}
