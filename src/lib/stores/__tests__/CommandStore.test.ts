import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { 
  commandHistory, 
  executeCommand, 
  undoCommand, 
  redoCommand, 
  canUndo, 
  canRedo, 
  clearCommandHistory 
} from '../CommandStore';
import type { Command } from '../CommandStore';

describe('CommandStore', () => {
  beforeEach(() => {
    clearCommandHistory();
  });

  it('should start with empty command history', () => {
    const $commandHistory = get(commandHistory);
    expect($commandHistory.commands).toHaveLength(0);
    expect($commandHistory.currentIndex).toBe(-1);
  });

  it('should execute and store commands', () => {
    const mockExecute = vi.fn();
    const mockUndo = vi.fn();
    
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: mockExecute,
      undo: mockUndo
    };

    executeCommand(command);

    expect(mockExecute).toHaveBeenCalledOnce();
    
    const $commandHistory = get(commandHistory);
    expect($commandHistory.commands).toHaveLength(1);
    expect($commandHistory.currentIndex).toBe(0);
  });

  it('should undo commands', () => {
    const mockExecute = vi.fn();
    const mockUndo = vi.fn();
    
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: mockExecute,
      undo: mockUndo
    };

    executeCommand(command);
    const undoResult = undoCommand();

    expect(undoResult).toBe(true);
    expect(mockUndo).toHaveBeenCalledOnce();
    
    const $commandHistory = get(commandHistory);
    expect($commandHistory.currentIndex).toBe(-1);
  });

  it('should redo commands', () => {
    const mockExecute = vi.fn();
    const mockUndo = vi.fn();
    
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: mockExecute,
      undo: mockUndo
    };

    executeCommand(command);
    undoCommand();
    
    mockExecute.mockClear(); // Clear previous calls
    const redoResult = redoCommand();

    expect(redoResult).toBe(true);
    expect(mockExecute).toHaveBeenCalledOnce();
    
    const $commandHistory = get(commandHistory);
    expect($commandHistory.currentIndex).toBe(0);
  });

  it('should check if undo is available', () => {
    expect(canUndo()).toBe(false);
    
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: vi.fn(),
      undo: vi.fn()
    };

    executeCommand(command);
    expect(canUndo()).toBe(true);
    
    undoCommand();
    expect(canUndo()).toBe(false);
  });

  it('should check if redo is available', () => {
    expect(canRedo()).toBe(false);
    
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: vi.fn(),
      undo: vi.fn()
    };

    executeCommand(command);
    expect(canRedo()).toBe(false);
    
    undoCommand();
    expect(canRedo()).toBe(true);
    
    redoCommand();
    expect(canRedo()).toBe(false);
  });

  it('should limit command history', () => {
    const maxCommands = 20;
    
    // Execute more commands than the limit
    for (let i = 0; i < maxCommands + 5; i++) {
      const command: Command = {
        id: `test-${i}`,
        type: 'test',
        timestamp: Date.now(),
        description: `Test command ${i}`,
        execute: vi.fn(),
        undo: vi.fn()
      };
      executeCommand(command);
    }

    const $commandHistory = get(commandHistory);
    expect($commandHistory.commands.length).toBeLessThanOrEqual(maxCommands);
  });

  it('should clear command history', () => {
    const command: Command = {
      id: 'test-1',
      type: 'test',
      timestamp: Date.now(),
      description: 'Test command',
      execute: vi.fn(),
      undo: vi.fn()
    };

    executeCommand(command);
    clearCommandHistory();

    const $commandHistory = get(commandHistory);
    expect($commandHistory.commands).toHaveLength(0);
    expect($commandHistory.currentIndex).toBe(-1);
  });
});
