<script lang="ts">
  import { onMount } from 'svelte';
  import ThemeSwitcher from './ThemeSwitcher.svelte';
  import CommandPalette from './CommandPalette.svelte';
  import { currentProject, projectStatus } from '../stores/ProjectStore';
  import { commandHistory } from '../stores/CommandStore';

  // Menu state
  let activeMenu: string | null = null;
  let showCommandPalette = false;

  // Project actions
  function newProject() {
    if (confirm('Create a new project? Unsaved changes will be lost.')) {
      // Reset to new project
      console.log('New project');
    }
  }

  function saveProject() {
    // Trigger manual save
    console.log('Save project');
  }

  function exportHTML() {
    // Export as HTML
    console.log('Export HTML');
  }

  function exportCSS() {
    // Export as CSS
    console.log('Export CSS');
  }

  function importProject() {
    // Import project
    console.log('Import project');
  }

  // Edit actions
  function undo() {
    // Trigger undo
    console.log('Undo');
  }

  function redo() {
    // Trigger redo
    console.log('Redo');
  }

  // View actions
  function toggleGrid() {
    console.log('Toggle grid');
  }

  function toggleGuides() {
    console.log('Toggle guides');
  }

  function zoomIn() {
    console.log('Zoom in');
  }

  function zoomOut() {
    console.log('Zoom out');
  }

  function resetZoom() {
    console.log('Reset zoom');
  }

  // Menu handling
  function toggleMenu(menu: string) {
    activeMenu = activeMenu === menu ? null : menu;
  }

  function closeMenus() {
    activeMenu = null;
  }

  // Keyboard shortcuts
  function handleKeydown(event: KeyboardEvent) {
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault();
      showCommandPalette = true;
    }
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeydown);
    document.addEventListener('click', closeMenus);

    return () => {
      document.removeEventListener('keydown', handleKeydown);
      document.removeEventListener('click', closeMenus);
    };
  });
</script>

<header class="header">
  <div class="header-left">
    <!-- Logo and Title -->
    <div class="logo-section">
      <div class="logo">🎨</div>
      <h1 class="title">Evolve UI</h1>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
      <!-- File Menu -->
      <div class="menu-container">
        <button
          class="menu-button"
          class:active={activeMenu === 'file'}
          onclick={(e) => { e.stopPropagation(); toggleMenu('file'); }}
        >
          File
        </button>
        {#if activeMenu === 'file'}
          <div class="dropdown-menu">
            <button class="menu-item" onclick={newProject}>
              <span>New Project</span>
              <span class="shortcut">Ctrl+N</span>
            </button>
            <button class="menu-item" onclick={saveProject}>
              <span>Save Project</span>
              <span class="shortcut">Ctrl+S</span>
            </button>
            <div class="menu-separator"></div>
            <button class="menu-item" onclick={importProject}>
              <span>Import Project</span>
            </button>
            <button class="menu-item" onclick={exportHTML}>
              <span>Export HTML</span>
            </button>
            <button class="menu-item" onclick={exportCSS}>
              <span>Export CSS</span>
            </button>
          </div>
        {/if}
      </div>

      <!-- Edit Menu -->
      <div class="menu-container">
        <button
          class="menu-button"
          class:active={activeMenu === 'edit'}
          onclick={(e) => { e.stopPropagation(); toggleMenu('edit'); }}
        >
          Edit
        </button>
        {#if activeMenu === 'edit'}
          <div class="dropdown-menu">
            <button class="menu-item" onclick={undo}>
              <span>Undo</span>
              <span class="shortcut">Ctrl+Z</span>
            </button>
            <button class="menu-item" onclick={redo}>
              <span>Redo</span>
              <span class="shortcut">Ctrl+Y</span>
            </button>
            <div class="menu-separator"></div>
            <button class="menu-item">
              <span>Copy</span>
              <span class="shortcut">Ctrl+C</span>
            </button>
            <button class="menu-item">
              <span>Paste</span>
              <span class="shortcut">Ctrl+V</span>
            </button>
            <button class="menu-item">
              <span>Delete</span>
              <span class="shortcut">Del</span>
            </button>
          </div>
        {/if}
      </div>

      <!-- View Menu -->
      <div class="menu-container">
        <button
          class="menu-button"
          class:active={activeMenu === 'view'}
          onclick={(e) => { e.stopPropagation(); toggleMenu('view'); }}
        >
          View
        </button>
        {#if activeMenu === 'view'}
          <div class="dropdown-menu">
            <button class="menu-item" onclick={toggleGrid}>
              <span>Toggle Grid</span>
              <span class="shortcut">Ctrl+G</span>
            </button>
            <button class="menu-item" onclick={toggleGuides}>
              <span>Toggle Guides</span>
            </button>
            <div class="menu-separator"></div>
            <button class="menu-item" onclick={zoomIn}>
              <span>Zoom In</span>
              <span class="shortcut">Ctrl++</span>
            </button>
            <button class="menu-item" onclick={zoomOut}>
              <span>Zoom Out</span>
              <span class="shortcut">Ctrl+-</span>
            </button>
            <button class="menu-item" onclick={resetZoom}>
              <span>Reset Zoom</span>
              <span class="shortcut">Ctrl+0</span>
            </button>
          </div>
        {/if}
      </div>

      <!-- Help Menu -->
      <div class="menu-container">
        <button
          class="menu-button"
          class:active={activeMenu === 'help'}
          onclick={(e) => { e.stopPropagation(); toggleMenu('help'); }}
        >
          Help
        </button>
        {#if activeMenu === 'help'}
          <div class="dropdown-menu">
            <button class="menu-item">
              <span>Keyboard Shortcuts</span>
              <span class="shortcut">?</span>
            </button>
            <button class="menu-item">
              <span>Documentation</span>
            </button>
            <div class="menu-separator"></div>
            <button class="menu-item">
              <span>About</span>
            </button>
          </div>
        {/if}
      </div>
    </nav>
  </div>

  <!-- Center Section - Project Info -->
  <div class="header-center">
    <div class="project-info">
      <span class="project-name">{$currentProject.name}</span>
      {#if $projectStatus.hasUnsavedChanges}
        <span class="unsaved-indicator" title="Unsaved changes">●</span>
      {/if}
    </div>
  </div>

  <!-- Right Section -->
  <div class="header-right">
    <!-- Command Palette Button -->
    <button
      class="icon-button"
      onclick={() => showCommandPalette = true}
      title="Command Palette (Ctrl+K)"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </button>

    <!-- Theme Switcher -->
    <ThemeSwitcher />

    <!-- Settings Button -->
    <button class="icon-button" title="Settings">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      </svg>
    </button>
  </div>
</header>

<!-- Command Palette -->
<CommandPalette />

<style>
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text);
    height: 3rem;
    position: relative;
    z-index: 100;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .logo {
    font-size: 1.5rem;
  }

  .title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-text);
    margin: 0;
  }

  .main-nav {
    display: flex;
    gap: 0.5rem;
  }

  .menu-container {
    position: relative;
  }

  .menu-button {
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    color: var(--color-text);
    cursor: pointer;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    font-size: 0.875rem;
  }

  .menu-button:hover,
  .menu-button.active {
    background-color: var(--color-hover);
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 0.5rem 0;
    z-index: 1000;
  }

  .menu-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    color: var(--color-text);
    cursor: pointer;
    font-size: 0.875rem;
    text-align: left;
    transition: background-color 0.2s;
  }

  .menu-item:hover {
    background-color: var(--color-hover);
  }

  .shortcut {
    font-size: 0.75rem;
    color: var(--color-secondary);
    font-family: monospace;
  }

  .menu-separator {
    height: 1px;
    background-color: var(--color-border);
    margin: 0.5rem 0;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .project-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .project-name {
    font-weight: 500;
    color: var(--color-text);
  }

  .unsaved-indicator {
    color: var(--color-primary);
    font-size: 1.25rem;
    line-height: 1;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .icon-button {
    padding: 0.5rem;
    background: transparent;
    border: none;
    color: var(--color-secondary);
    cursor: pointer;
    border-radius: 0.375rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-button:hover {
    background-color: var(--color-hover);
    color: var(--color-text);
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    .header-left {
      gap: 1rem;
    }

    .main-nav {
      gap: 0.25rem;
    }

    .menu-button {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }
  }

  @media (max-width: 768px) {
    .header {
      padding: 0.5rem;
    }

    .logo-section {
      gap: 0.25rem;
    }

    .title {
      font-size: 1rem;
    }

    .main-nav {
      display: none;
    }

    .header-center {
      display: none;
    }
  }
</style>

<style>
  /* Use theme variables */
  header {
    background-color: var(--color-surface);
    color: var(--color-text);
    border-bottom: 1px solid var(--color-border);
  }

  button {
    background-color: transparent;
    color: var(--color-text);
  }

  button:hover {
    background-color: var(--color-hover);
  }
</style>
