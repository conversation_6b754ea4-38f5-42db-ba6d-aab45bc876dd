<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { currentProject } from '../stores/ProjectStore';
  import type { CanvasComponent } from '../stores/ProjectStore';
  import CanvasGrid from './CanvasGrid.svelte';
  import AlignmentGuides from './AlignmentGuides.svelte';
  import { handleKeyboardEvent } from '../stores/KeyboardStore';

  // Canvas properties
  export let canvasWidth = 1200;
  export let canvasHeight = 800;
  export let onComponentSelect: (component: CanvasComponent) => void = () => {};

  // Canvas state
  let canvasScale = 1;
  let canvasOffset = { x: 0, y: 0 };
  let selectedComponent: CanvasComponent | null = null;
  let draggedComponent: CanvasComponent | null = null;
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };

  // Canvas element references
  let canvasContainer: HTMLDivElement;
  let gridComponent: CanvasGrid;
  let guidesComponent: AlignmentGuides;

  // Reactive canvas components from project
  $: canvasComponents = $currentProject.canvasComponents;

  // Handle component selection
  function selectComponent(component: CanvasComponent): void {
    selectedComponent = component;
    onComponentSelect(component);
  }

  // Handle drag start
  function handleDragStart(event: MouseEvent, component: CanvasComponent): void {
    if (component.locked) return;

    isDragging = true;
    draggedComponent = component;
    
    const rect = canvasContainer.getBoundingClientRect();
    dragOffset = {
      x: event.clientX - rect.left - component.position.x * canvasScale,
      y: event.clientY - rect.top - component.position.y * canvasScale
    };

    // Update alignment guides
    if (guidesComponent) {
      guidesComponent.updateActiveGuides(component, canvasComponents.filter(c => c.id !== component.id));
    }

    event.preventDefault();
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  }

  // Handle drag move
  function handleDragMove(event: MouseEvent): void {
    if (!isDragging || !draggedComponent) return;

    const rect = canvasContainer.getBoundingClientRect();
    let newX = (event.clientX - rect.left - dragOffset.x) / canvasScale;
    let newY = (event.clientY - rect.top - dragOffset.y) / canvasScale;

    // Snap to grid if enabled
    if (gridComponent && $currentProject.settings.snapToGrid) {
      const snapped = gridComponent.snapToGridPosition(newX, newY);
      newX = snapped.x;
      newY = snapped.y;
    }

    // Snap to alignment guides if enabled
    if (guidesComponent && $currentProject.settings.showGuides) {
      const snapped = guidesComponent.snapToGuides(
        { x: newX, y: newY },
        draggedComponent.size,
        canvasComponents.filter(c => c.id !== draggedComponent.id)
      );
      newX = snapped.x;
      newY = snapped.y;
    }

    // Constrain to canvas bounds
    newX = Math.max(0, Math.min(newX, canvasWidth - draggedComponent.size.width));
    newY = Math.max(0, Math.min(newY, canvasHeight - draggedComponent.size.height));

    // Update component position
    draggedComponent.position = { x: newX, y: newY };
    
    // Update project
    currentProject.update(project => ({
      ...project,
      canvasComponents: project.canvasComponents.map(c => 
        c.id === draggedComponent.id ? draggedComponent : c
      )
    }));

    // Update alignment guides
    if (guidesComponent) {
      guidesComponent.updateActiveGuides(draggedComponent, canvasComponents.filter(c => c.id !== draggedComponent.id));
    }
  }

  // Handle drag end
  function handleDragEnd(): void {
    isDragging = false;
    draggedComponent = null;

    // Clear alignment guides
    if (guidesComponent) {
      guidesComponent.clearActiveGuides();
    }

    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
  }

  // Handle canvas zoom
  function handleZoom(event: WheelEvent): void {
    event.preventDefault();
    
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(3, canvasScale * zoomFactor));
    
    if (newScale !== canvasScale) {
      canvasScale = newScale;
    }
  }

  // Zoom controls
  function zoomIn(): void {
    canvasScale = Math.min(3, canvasScale * 1.2);
  }

  function zoomOut(): void {
    canvasScale = Math.max(0.1, canvasScale * 0.8);
  }

  function resetZoom(): void {
    canvasScale = 1;
  }

  // Handle keyboard events
  function handleKeydown(event: KeyboardEvent): void {
    handleKeyboardEvent(event);
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  onDestroy(() => {
    document.removeEventListener('keydown', handleKeydown);
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
  });
</script>

<div class="enhanced-canvas">
  <!-- Canvas Container -->
  <div 
    class="canvas-viewport"
    bind:this={canvasContainer}
    onwheel={handleZoom}
  >
    <div 
      class="canvas-content"
      style="
        width: {canvasWidth}px; 
        height: {canvasHeight}px; 
        transform: scale({canvasScale}) translate({canvasOffset.x}px, {canvasOffset.y}px);
      "
    >
      <!-- Grid Overlay -->
      <CanvasGrid 
        bind:this={gridComponent}
        {canvasWidth}
        {canvasHeight}
        visible={$currentProject.settings.gridEnabled}
        gridSize={$currentProject.settings.gridSize}
        snapToGrid={$currentProject.settings.snapToGrid}
      />

      <!-- Alignment Guides -->
      <AlignmentGuides 
        bind:this={guidesComponent}
        {canvasWidth}
        {canvasHeight}
        components={canvasComponents}
        activeComponent={draggedComponent}
        visible={$currentProject.settings.showGuides}
      />

      <!-- Canvas Components -->
      {#each canvasComponents as component (component.id)}
        <div
          class="canvas-component"
          class:selected={selectedComponent?.id === component.id}
          class:locked={component.locked}
          class:hidden={!component.visible}
          style="
            left: {component.position.x}px;
            top: {component.position.y}px;
            width: {component.size.width}px;
            height: {component.size.height}px;
            z-index: {component.zIndex};
          "
          onclick={() => selectComponent(component)}
          onmousedown={(e) => handleDragStart(e, component)}
          role="button"
          tabindex="0"
        >
          {@html component.html}
          
          <!-- Selection Handles -->
          {#if selectedComponent?.id === component.id && !component.locked}
            <div class="selection-handles">
              <div class="handle nw"></div>
              <div class="handle ne"></div>
              <div class="handle sw"></div>
              <div class="handle se"></div>
              <div class="handle n"></div>
              <div class="handle s"></div>
              <div class="handle e"></div>
              <div class="handle w"></div>
            </div>
          {/if}
        </div>
      {/each}

      <!-- Empty State -->
      {#if canvasComponents.length === 0}
        <div class="empty-state">
          <div class="empty-icon">🎨</div>
          <div class="empty-title">Your Canvas is Empty</div>
          <div class="empty-description">
            Drag components from the library to start building your design
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Canvas Controls -->
  <div class="canvas-controls">
    <div class="zoom-controls">
      <button class="control-btn" onclick={zoomOut} title="Zoom Out">−</button>
      <span class="zoom-level">{Math.round(canvasScale * 100)}%</span>
      <button class="control-btn" onclick={zoomIn} title="Zoom In">+</button>
      <button class="control-btn" onclick={resetZoom} title="Reset Zoom">100%</button>
    </div>
    
    <div class="canvas-info">
      <span class="info-item">
        {canvasComponents.length} component{canvasComponents.length !== 1 ? 's' : ''}
      </span>
      {#if selectedComponent}
        <span class="info-item">
          Selected: {selectedComponent.id}
        </span>
      {/if}
    </div>
  </div>
</div>

<style>
  .enhanced-canvas {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: var(--color-background);
    overflow: hidden;
  }

  .canvas-viewport {
    width: 100%;
    height: calc(100% - 60px);
    overflow: auto;
    background: 
      radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0);
    background-size: 20px 20px;
    cursor: grab;
  }

  .canvas-viewport:active {
    cursor: grabbing;
  }

  .canvas-content {
    position: relative;
    margin: 50px;
    background: white;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform-origin: top left;
    transition: transform 0.2s ease;
  }

  .canvas-component {
    position: absolute;
    cursor: move;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    user-select: none;
  }

  .canvas-component:hover {
    border-color: rgba(0, 123, 255, 0.5);
    box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.2);
  }

  .canvas-component.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 1px #007bff;
  }

  .canvas-component.locked {
    cursor: not-allowed;
    opacity: 0.7;
  }

  .canvas-component.hidden {
    display: none;
  }

  .selection-handles {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    pointer-events: none;
  }

  .handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #007bff;
    border: 1px solid white;
    border-radius: 2px;
    pointer-events: all;
  }

  .handle.nw { top: 0; left: 0; cursor: nw-resize; }
  .handle.ne { top: 0; right: 0; cursor: ne-resize; }
  .handle.sw { bottom: 0; left: 0; cursor: sw-resize; }
  .handle.se { bottom: 0; right: 0; cursor: se-resize; }
  .handle.n { top: 0; left: 50%; transform: translateX(-50%); cursor: n-resize; }
  .handle.s { bottom: 0; left: 50%; transform: translateX(-50%); cursor: s-resize; }
  .handle.e { top: 50%; right: 0; transform: translateY(-50%); cursor: e-resize; }
  .handle.w { top: 50%; left: 0; transform: translateY(-50%); cursor: w-resize; }

  .empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--color-secondary);
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .empty-description {
    font-size: 1rem;
    opacity: 0.8;
  }

  .canvas-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: var(--color-surface);
    border-top: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
  }

  .zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .control-btn {
    padding: 0.5rem;
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    color: var(--color-text);
    cursor: pointer;
    transition: all 0.2s;
    min-width: 2.5rem;
    text-align: center;
  }

  .control-btn:hover {
    background: var(--color-hover);
  }

  .zoom-level {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--color-text);
    min-width: 4rem;
    text-align: center;
  }

  .canvas-info {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--color-secondary);
  }

  .info-item {
    font-family: monospace;
  }

  /* Dark mode adjustments */
  :global(.dark) .canvas-content {
    background: var(--color-surface);
    border-color: var(--color-border);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .canvas-controls {
      flex-direction: column;
      height: auto;
      padding: 0.5rem;
      gap: 0.5rem;
    }

    .canvas-info {
      font-size: 0.75rem;
    }
  }
</style>
