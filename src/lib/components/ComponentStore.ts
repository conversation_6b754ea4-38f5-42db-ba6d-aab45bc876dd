// ComponentStore.ts
import { writable } from 'svelte/store';

// Define component types
export interface ComponentProperty {
  id: string;
  classes: string;
  [key: string]: any;
}

export interface Component {
  id: number;
  name: string;
  category: string;
  html: string;
  icon: string;
  properties: ComponentProperty;
}

// Initial component data - Comprehensive Bootstrap 5.x components
const initialComponents: Component[] = [
  // Layout Components
  {
    id: 1,
    name: 'Container',
    category: 'Layout',
    html: '<div class="container"><p class="text-muted">Container content</p></div>',
    icon: '📦',
    properties: {
      id: 'container-1',
      classes: 'container',
      fluid: false,
      breakpoint: 'none' // none, sm, md, lg, xl, xxl
    }
  },
  {
    id: 2,
    name: 'Container Fluid',
    category: 'Layout',
    html: '<div class="container-fluid"><p class="text-muted">Fluid container content</p></div>',
    icon: '📦',
    properties: {
      id: 'container-fluid-1',
      classes: 'container-fluid',
      fluid: true,
      breakpoint: 'none'
    }
  },
  {
    id: 3,
    name: 'Row',
    category: 'Layout',
    html: '<div class="row"><div class="col"><p class="text-muted">Row content</p></div></div>',
    icon: '↔️',
    properties: {
      id: 'row-1',
      classes: 'row',
      gutters: true,
      horizontalGutters: true,
      verticalGutters: true
    }
  },
  {
    id: 4,
    name: 'Column',
    category: 'Layout',
    html: '<div class="col"><p class="text-muted">Column content</p></div>',
    icon: '⬇️',
    properties: {
      id: 'col-1',
      classes: 'col',
      size: '', // 1-12 or empty for auto
      sizeSm: '',
      sizeMd: '',
      sizeLg: '',
      sizeXl: '',
      sizeXxl: ''
    }
  },
  {
    id: 5,
    name: 'Flexbox Container',
    category: 'Layout',
    html: '<div class="d-flex"><div class="p-2">Flex item 1</div><div class="p-2">Flex item 2</div></div>',
    icon: '🔄',
    properties: {
      id: 'flex-1',
      classes: 'd-flex',
      direction: 'row',
      justify: 'start',
      align: 'start',
      wrap: 'nowrap'
    }
  },

  // Form Components
  {
    id: 4,
    name: 'Button',
    category: 'Form',
    html: '<button class="btn btn-primary">Button Text</button>',
    icon: '🔘',
    properties: {
      id: 'button-1',
      classes: 'btn btn-primary',
      text: 'Button Text',
      size: 'default', // default, sm, lg
      style: 'primary' // primary, secondary, success, danger, warning, info, light, dark
    }
  },
  {
    id: 5,
    name: 'Card',
    category: 'Container',
    html: '<div class="card"><div class="card-body"><h5 class="card-title">Card Title</h5><p class="card-text">Some quick example text.</p></div></div>',
    icon: '🗃️',
    properties: {
      id: 'card-1',
      classes: 'card',
      title: 'Card Title',
      text: 'Some quick example text.',
      width: '18rem'
    }
  },
  {
    id: 6,
    name: 'Navbar',
    category: 'Navigation',
    html: '<nav class="navbar navbar-expand-lg navbar-light bg-light"><div class="container-fluid"><a class="navbar-brand" href="#">Navbar</a></div></nav>',
    icon: '🧭',
    properties: {
      id: 'navbar-1',
      classes: 'navbar navbar-expand-lg navbar-light bg-light',
      brand: 'Navbar',
      container: 'container-fluid' // container, container-fluid
    }
  },
  {
    id: 7,
    name: 'Alert',
    category: 'Feedback',
    html: '<div class="alert alert-primary" role="alert">This is an alert message!</div>',
    icon: '⚠️',
    properties: {
      id: 'alert-1',
      classes: 'alert alert-primary',
      text: 'This is an alert message!',
      style: 'primary', // primary, secondary, success, danger, warning, info, light, dark
      dismissible: false
    }
  },

  // Navigation Components
  {
    id: 6,
    name: 'Navbar',
    category: 'Navigation',
    html: '<nav class="navbar navbar-expand-lg navbar-light bg-light"><div class="container-fluid"><a class="navbar-brand" href="#">Navbar</a><button class="navbar-toggler" type="button"><span class="navbar-toggler-icon"></span></button><div class="navbar-nav"><a class="nav-link active" href="#">Home</a><a class="nav-link" href="#">Link</a></div></div></nav>',
    icon: '🧭',
    properties: {
      id: 'navbar-1',
      classes: 'navbar navbar-expand-lg navbar-light bg-light',
      brand: 'Navbar',
      expand: 'lg',
      theme: 'light',
      background: 'light'
    }
  },
  {
    id: 7,
    name: 'Breadcrumb',
    category: 'Navigation',
    html: '<nav aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="#">Home</a></li><li class="breadcrumb-item"><a href="#">Library</a></li><li class="breadcrumb-item active" aria-current="page">Data</li></ol></nav>',
    icon: '🍞',
    properties: {
      id: 'breadcrumb-1',
      classes: 'breadcrumb',
      items: ['Home', 'Library', 'Data']
    }
  },
  {
    id: 8,
    name: 'Pagination',
    category: 'Navigation',
    html: '<nav aria-label="Page navigation"><ul class="pagination"><li class="page-item"><a class="page-link" href="#">Previous</a></li><li class="page-item"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">2</a></li><li class="page-item"><a class="page-link" href="#">3</a></li><li class="page-item"><a class="page-link" href="#">Next</a></li></ul></nav>',
    icon: '📄',
    properties: {
      id: 'pagination-1',
      classes: 'pagination',
      size: 'default',
      alignment: 'start'
    }
  },

  // Form Components
  {
    id: 9,
    name: 'Input',
    category: 'Forms',
    html: '<div class="mb-3"><label for="input-1" class="form-label">Input Label</label><input type="text" class="form-control" id="input-1" placeholder="Enter text"></div>',
    icon: '📝',
    properties: {
      id: 'input-1',
      classes: 'form-control',
      type: 'text',
      placeholder: 'Enter text',
      label: 'Input Label',
      required: false,
      disabled: false
    }
  },
  {
    id: 10,
    name: 'Textarea',
    category: 'Forms',
    html: '<div class="mb-3"><label for="textarea-1" class="form-label">Textarea Label</label><textarea class="form-control" id="textarea-1" rows="3" placeholder="Enter text"></textarea></div>',
    icon: '📄',
    properties: {
      id: 'textarea-1',
      classes: 'form-control',
      rows: 3,
      placeholder: 'Enter text',
      label: 'Textarea Label',
      required: false,
      disabled: false
    }
  },
  {
    id: 11,
    name: 'Select',
    category: 'Forms',
    html: '<div class="mb-3"><label for="select-1" class="form-label">Select Label</label><select class="form-select" id="select-1"><option selected>Choose...</option><option value="1">One</option><option value="2">Two</option><option value="3">Three</option></select></div>',
    icon: '📋',
    properties: {
      id: 'select-1',
      classes: 'form-select',
      label: 'Select Label',
      options: ['Choose...', 'One', 'Two', 'Three'],
      multiple: false,
      disabled: false
    }
  },
  {
    id: 12,
    name: 'Checkbox',
    category: 'Forms',
    html: '<div class="form-check"><input class="form-check-input" type="checkbox" value="" id="checkbox-1"><label class="form-check-label" for="checkbox-1">Default checkbox</label></div>',
    icon: '☑️',
    properties: {
      id: 'checkbox-1',
      classes: 'form-check-input',
      label: 'Default checkbox',
      checked: false,
      disabled: false
    }
  },
  {
    id: 13,
    name: 'Radio',
    category: 'Forms',
    html: '<div class="form-check"><input class="form-check-input" type="radio" name="radio-1" id="radio-1" value="option1"><label class="form-check-label" for="radio-1">Default radio</label></div>',
    icon: '🔘',
    properties: {
      id: 'radio-1',
      classes: 'form-check-input',
      name: 'radio-1',
      label: 'Default radio',
      value: 'option1',
      checked: false,
      disabled: false
    }
  },

  // Component Elements
  {
    id: 14,
    name: 'Button',
    category: 'Components',
    html: '<button type="button" class="btn btn-primary">Primary Button</button>',
    icon: '🔘',
    properties: {
      id: 'button-1',
      classes: 'btn btn-primary',
      text: 'Primary Button',
      variant: 'primary',
      size: 'default',
      disabled: false,
      outline: false
    }
  },
  {
    id: 15,
    name: 'Card',
    category: 'Components',
    html: '<div class="card" style="width: 18rem;"><div class="card-body"><h5 class="card-title">Card title</h5><p class="card-text">Some quick example text to build on the card title and make up the bulk of the card\'s content.</p><a href="#" class="btn btn-primary">Go somewhere</a></div></div>',
    icon: '🃏',
    properties: {
      id: 'card-1',
      classes: 'card',
      title: 'Card title',
      text: 'Some quick example text to build on the card title and make up the bulk of the card\'s content.',
      buttonText: 'Go somewhere',
      width: '18rem'
    }
  },
  {
    id: 16,
    name: 'Alert',
    category: 'Components',
    html: '<div class="alert alert-primary" role="alert">A simple primary alert—check it out!</div>',
    icon: '⚠️',
    properties: {
      id: 'alert-1',
      classes: 'alert alert-primary',
      text: 'A simple primary alert—check it out!',
      variant: 'primary',
      dismissible: false
    }
  },
  {
    id: 17,
    name: 'Modal',
    category: 'Components',
    html: '<div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title">Modal title</h5><button type="button" class="btn-close" aria-label="Close"></button></div><div class="modal-body">Modal body text goes here.</div><div class="modal-footer"><button type="button" class="btn btn-secondary">Close</button><button type="button" class="btn btn-primary">Save changes</button></div></div></div>',
    icon: '🪟',
    properties: {
      id: 'modal-1',
      classes: 'modal-dialog',
      title: 'Modal title',
      body: 'Modal body text goes here.',
      size: 'default',
      centered: false
    }
  },

  // Content Components
  {
    id: 18,
    name: 'Heading',
    category: 'Content',
    html: '<h1>Heading 1</h1>',
    icon: '📰',
    properties: {
      id: 'heading-1',
      classes: '',
      text: 'Heading 1',
      level: 1,
      display: false
    }
  },
  {
    id: 19,
    name: 'Paragraph',
    category: 'Content',
    html: '<p>This is a paragraph of text. It can contain multiple sentences and will wrap naturally.</p>',
    icon: '📝',
    properties: {
      id: 'paragraph-1',
      classes: '',
      text: 'This is a paragraph of text. It can contain multiple sentences and will wrap naturally.',
      lead: false
    }
  },
  {
    id: 20,
    name: 'Image',
    category: 'Content',
    html: '<img src="https://via.placeholder.com/300x200" class="img-fluid" alt="Placeholder image">',
    icon: '🖼️',
    properties: {
      id: 'image-1',
      classes: 'img-fluid',
      src: 'https://via.placeholder.com/300x200',
      alt: 'Placeholder image',
      responsive: true,
      rounded: false,
      thumbnail: false
    }
  },
  {
    id: 21,
    name: 'Table',
    category: 'Content',
    html: '<table class="table"><thead><tr><th scope="col">#</th><th scope="col">First</th><th scope="col">Last</th><th scope="col">Handle</th></tr></thead><tbody><tr><th scope="row">1</th><td>Mark</td><td>Otto</td><td>@mdo</td></tr><tr><th scope="row">2</th><td>Jacob</td><td>Thornton</td><td>@fat</td></tr></tbody></table>',
    icon: '📊',
    properties: {
      id: 'table-1',
      classes: 'table',
      striped: false,
      bordered: false,
      hover: false,
      small: false,
      responsive: false
    }
  }
];

// Create stores
export const components = writable<Component[]>(initialComponents);
export const selectedComponent = writable<Component | null>(null);
export const selectedElement = writable<string | null>(null);
export const isDraggingOver = writable<boolean>(false);

// Search and filter stores
export const searchQuery = writable<string>('');
export const selectedCategory = writable<string>('All');

// Get unique categories
export const categories = derived(components, ($components) => {
  const cats = ['All', ...new Set($components.map(c => c.category))];
  return cats;
});

// Filtered components based on search and category
export const filteredComponents = derived(
  [components, searchQuery, selectedCategory],
  ([$components, $searchQuery, $selectedCategory]) => {
    let filtered = $components;

    // Filter by category
    if ($selectedCategory !== 'All') {
      filtered = filtered.filter(c => c.category === $selectedCategory);
    }

    // Filter by search query
    if ($searchQuery.trim()) {
      const query = $searchQuery.toLowerCase();
      filtered = filtered.filter(c =>
        c.name.toLowerCase().includes(query) ||
        c.category.toLowerCase().includes(query)
      );
    }

    return filtered;
  }
);

// Function to update component HTML based on properties
export function updateComponentHTML(component: Component): void {
  if (!component) return;

  const props = component.properties;

  switch (component.name) {
    case 'Container':
      component.html = `<div class="${props.fluid ? 'container-fluid' : 'container'}${props.breakpoint !== 'none' ? `-${props.breakpoint}` : ''}" id="${props.id}"><p class="text-muted">Container content</p></div>`;
      break;

    case 'Container Fluid':
      component.html = `<div class="container-fluid" id="${props.id}"><p class="text-muted">Fluid container content</p></div>`;
      break;

    case 'Row':
      let rowClasses = 'row';
      if (!props.gutters) rowClasses += ' g-0';
      if (!props.horizontalGutters) rowClasses += ' gx-0';
      if (!props.verticalGutters) rowClasses += ' gy-0';
      component.html = `<div class="${rowClasses}" id="${props.id}"><div class="col"><p class="text-muted">Row content</p></div></div>`;
      break;

    case 'Column':
      let colClasses = 'col';
      if (props.size) colClasses = `col-${props.size}`;
      if (props.sizeSm) colClasses += ` col-sm-${props.sizeSm}`;
      if (props.sizeMd) colClasses += ` col-md-${props.sizeMd}`;
      if (props.sizeLg) colClasses += ` col-lg-${props.sizeLg}`;
      if (props.sizeXl) colClasses += ` col-xl-${props.sizeXl}`;
      if (props.sizeXxl) colClasses += ` col-xxl-${props.sizeXxl}`;
      component.html = `<div class="${colClasses}" id="${props.id}"><p class="text-muted">Column content</p></div>`;
      break;

    case 'Button':
      let btnClasses = 'btn';
      if (props.outline) {
        btnClasses += ` btn-outline-${props.variant}`;
      } else {
        btnClasses += ` btn-${props.variant}`;
      }
      if (props.size !== 'default') btnClasses += ` btn-${props.size}`;
      component.html = `<button type="button" class="${btnClasses}" id="${props.id}"${props.disabled ? ' disabled' : ''}>${props.text}</button>`;
      break;

    case 'Card':
      component.html = `<div class="card" id="${props.id}" style="width: ${props.width};"><div class="card-body"><h5 class="card-title">${props.title}</h5><p class="card-text">${props.text}</p><a href="#" class="btn btn-primary">${props.buttonText}</a></div></div>`;
      break;

    case 'Alert':
      let alertClasses = `alert alert-${props.variant}`;
      if (props.dismissible) alertClasses += ' alert-dismissible fade show';
      let alertHTML = `<div class="${alertClasses}" role="alert" id="${props.id}">${props.text}`;
      if (props.dismissible) {
        alertHTML += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
      }
      alertHTML += '</div>';
      component.html = alertHTML;
      break;

    case 'Input':
      component.html = `<div class="mb-3"><label for="${props.id}" class="form-label">${props.label}</label><input type="${props.type}" class="${props.classes}" id="${props.id}" placeholder="${props.placeholder}"${props.required ? ' required' : ''}${props.disabled ? ' disabled' : ''}></div>`;
      break;

    case 'Textarea':
      component.html = `<div class="mb-3"><label for="${props.id}" class="form-label">${props.label}</label><textarea class="${props.classes}" id="${props.id}" rows="${props.rows}" placeholder="${props.placeholder}"${props.required ? ' required' : ''}${props.disabled ? ' disabled' : ''}></textarea></div>`;
      break;

    case 'Heading':
      let headingClasses = props.display ? `display-${props.level}` : '';
      component.html = `<h${props.level} class="${headingClasses}" id="${props.id}">${props.text}</h${props.level}>`;
      break;

    case 'Paragraph':
      let pClasses = props.lead ? 'lead' : '';
      component.html = `<p class="${pClasses}" id="${props.id}">${props.text}</p>`;
      break;

    case 'Image':
      let imgClasses = [];
      if (props.responsive) imgClasses.push('img-fluid');
      if (props.rounded) imgClasses.push('rounded');
      if (props.thumbnail) imgClasses.push('img-thumbnail');
      component.html = `<img src="${props.src}" class="${imgClasses.join(' ')}" alt="${props.alt}" id="${props.id}">`;
      break;

    default:
      // For components that don't need special HTML generation, keep existing HTML
      break;
  }

  // Update the component classes if they've been modified
  if (props.classes && component.html) {
    // This is a simplified approach - in a real implementation, you'd want more sophisticated class management
    component.properties.classes = props.classes;
  }
}

// Helper function to generate HTML for a component based on its properties
export function generateComponentHTML(component: Component): string {
  if (!component) return '';

  const props = component.properties;
  let html = '';

  switch(component.name) {
    case 'Container':
      // Determine container class based on fluid and breakpoint properties
      let containerClass = props.classes;
      if (props.fluid && props.breakpoint === 'none') {
        containerClass = 'container-fluid';
      } else if (props.breakpoint !== 'none') {
        containerClass = `container-${props.breakpoint}`;
      }

      // Generate container HTML
      html = `<div id="${props.id}" class="${containerClass}"></div>`;
      break;

    case 'Row':
      // Determine row class based on gutter properties
      let rowClass = props.classes;
      if (!props.gutters) {
        rowClass += ' g-0';
      } else {
        if (!props.horizontalGutters) {
          rowClass += ' gx-0';
        }
        if (!props.verticalGutters) {
          rowClass += ' gy-0';
        }
      }

      // Generate row HTML
      html = `<div id="${props.id}" class="${rowClass}"></div>`;
      break;

    case 'Column':
      // Determine column class based on size properties
      let colClass = props.classes;

      // Default size (xs)
      if (props.size) {
        colClass = colClass.replace(/col(-\d+)?/, `col-${props.size}`);
      }

      // Responsive sizes
      const breakpoints = ['sm', 'md', 'lg', 'xl', 'xxl'];
      breakpoints.forEach(bp => {
        const sizeProp = `size${bp.charAt(0).toUpperCase() + bp.slice(1)}`;
        if (props[sizeProp]) {
          colClass += ` col-${bp}-${props[sizeProp]}`;
        }
      });

      // Generate column HTML
      html = `<div id="${props.id}" class="${colClass}"></div>`;
      break;

    case 'Button':
      // Determine button size class
      let sizeClass = '';
      if (props.size === 'sm') sizeClass = 'btn-sm';
      else if (props.size === 'lg') sizeClass = 'btn-lg';

      // Generate button HTML
      html = `<button id="${props.id}" class="${props.classes}">${props.text}</button>`;
      break;

    case 'Card':
      // Generate card HTML
      html = `
        <div id="${props.id}" class="${props.classes}" style="width: ${props.width};">
          <div class="card-body">
            <h5 class="card-title">${props.title}</h5>
            <p class="card-text">${props.text}</p>
          </div>
        </div>
      `;
      break;

    case 'Navbar':
      // Generate navbar HTML
      html = `
        <nav id="${props.id}" class="${props.classes}">
          <div class="${props.container}">
            <a class="navbar-brand" href="#">${props.brand}</a>
          </div>
        </nav>
      `;
      break;

    case 'Alert':
      // Generate alert HTML
      const dismissButton = props.dismissible ?
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' : '';
      const dismissClass = props.dismissible ? ' alert-dismissible fade show' : '';

      html = `
        <div id="${props.id}" class="${props.classes}${dismissClass}" role="alert">
          ${props.text}
          ${dismissButton}
        </div>
      `;
      break;

    case 'Modal':
      // Determine modal size class
      let modalSizeClass = '';
      if (props.size === 'sm') modalSizeClass = 'modal-sm';
      else if (props.size === 'lg') modalSizeClass = 'modal-lg';

      // Determine if modal is centered
      const centeredClass = props.centered ? 'modal-dialog-centered' : '';

      // Generate modal HTML
      html = `
        <div id="${props.id}" class="${props.classes} ${modalSizeClass} ${centeredClass}">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${props.title}</h5>
            </div>
            <div class="modal-body">
              ${props.body}
            </div>
          </div>
        </div>
      `;
      break;

    default:
      html = component.html;
  }

  return html;
}


