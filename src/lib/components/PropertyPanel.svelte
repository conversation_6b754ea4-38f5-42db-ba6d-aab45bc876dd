<script lang="ts">
  import { selectedComponent } from './ComponentStore';
  import { updateComponentHTML } from './ComponentStore';
  import type { Component } from './ComponentStore';
  import PropertyEditors from './PropertyEditors.svelte';
  import CSSCodeViewer from './CSSCodeViewer.svelte';

  // Function to update iframe content
  export let onUpdateComponent: (component: Component) => void;

  // Collapsed state
  export let collapsed: boolean = false;

  // Panel tabs
  let activeTab: 'properties' | 'css' = 'properties';

  // Debounce function to limit updates for text inputs
  function debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout>;
    return function(...args: Parameters<T>): void {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  // Type-safe event handlers
  function handleInputChange(e: Event, property: string, isTextContent: boolean = false): void {
    if (!$selectedComponent || !e.target) return;
    const target = e.target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
    $selectedComponent.properties[property] = target.value;
    updateComponentHTML($selectedComponent);

    // Use direct update for better performance
    onUpdateComponent($selectedComponent);
  }

  // Debounced version for text content to reduce flickering
  const debouncedInputChange = debounce((e: Event, property: string) => {
    handleInputChange(e, property, true);
  }, 150);

  // Switch tabs
  function switchTab(tab: 'properties' | 'css') {
    activeTab = tab;
  }

  // Debounced version for all text inputs (shorter delay for better UX)
  const debouncedTextChange = debounce((e: Event, property: string) => {
    handleInputChange(e, property, false);
  }, 100);

  function handleCheckboxChange(e: Event, property: string): void {
    if (!$selectedComponent || !e.target) return;
    const target = e.target as HTMLInputElement;
    $selectedComponent.properties[property] = target.checked;
    updateComponentHTML($selectedComponent);
    onUpdateComponent($selectedComponent);
  }

  function handleStyleChange(e: Event, property: string, pattern: RegExp, prefix: string): void {
    if (!$selectedComponent || !e.target) return;
    const target = e.target as HTMLSelectElement;
    $selectedComponent.properties[property] = target.value;

    // Update classes to reflect the new style
    const classes = $selectedComponent.properties.classes.replace(pattern, `${prefix}-${target.value}`);
    $selectedComponent.properties.classes = classes;

    updateComponentHTML($selectedComponent);
    onUpdateComponent($selectedComponent);
  }
</script>

<aside class="w-full h-full flex flex-col property-panel" class:collapsed>
  <!-- Header with Tabs -->
  <div class="panel-header">
    <div class="panel-title">
      {#if $selectedComponent}
        {$selectedComponent.name} Properties
      {:else}
        Properties
      {/if}
    </div>

    {#if $selectedComponent}
      <div class="panel-tabs">
        <button
          class="tab-button"
          class:active={activeTab === 'properties'}
          onclick={() => switchTab('properties')}
        >
          Properties
        </button>
        <button
          class="tab-button"
          class:active={activeTab === 'css'}
          onclick={() => switchTab('css')}
        >
          CSS
        </button>
      </div>
    {/if}
  </div>

  <!-- Content -->
  <div class="overflow-y-auto flex-1">
    {#if $selectedComponent}
      {#if activeTab === 'properties'}
        <div class="p-4">
          <!-- Basic Properties -->
          <div class="property-section">
            <h4 class="property-section-title">Basic</h4>

            <div class="property-grid">
              <div class="property-item">
                <label for="component-id" class="property-label">ID</label>
                <input
                  id="component-id"
                  type="text"
                  class="property-input"
                  value={$selectedComponent.properties.id}
                  oninput={(e) => debouncedInputChange(e, 'id')}
                />
              </div>

              <div class="property-item">
                <label for="component-classes" class="property-label">CSS Classes</label>
                <input
                  id="component-classes"
                  type="text"
                  class="property-input"
                  value={$selectedComponent.properties.classes}
                  oninput={(e) => debouncedInputChange(e, 'classes')}
                  placeholder="Enter CSS classes"
                />
              </div>
            </div>
          </div>

          <!-- Advanced Property Editors -->
          <PropertyEditors {onUpdateComponent} />

          <!-- Component-Specific Properties -->
          {#if $selectedComponent.name === 'Button'}
            <div class="property-section">
              <h4 class="property-section-title">Button Properties</h4>

              <div class="property-grid">
                <div class="property-item">
                  <label class="property-label">Text</label>
                  <input
                    type="text"
                    class="property-input"
                    value={$selectedComponent.properties.text}
                    oninput={(e) => debouncedInputChange(e, 'text')}
                  />
                </div>

                <div class="property-item">
                  <label class="property-label">Variant</label>
                  <select
                    class="property-input"
                    value={$selectedComponent.properties.variant}
                    onchange={(e) => handleInputChange(e, 'variant')}
                  >
                    <option value="primary">Primary</option>
                    <option value="secondary">Secondary</option>
                    <option value="success">Success</option>
                    <option value="danger">Danger</option>
                    <option value="warning">Warning</option>
                    <option value="info">Info</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                <div class="property-item">
                  <label class="property-label">Size</label>
                  <select
                    class="property-input"
                    value={$selectedComponent.properties.size}
                    onchange={(e) => handleInputChange(e, 'size')}
                  >
                    <option value="sm">Small</option>
                    <option value="default">Default</option>
                    <option value="lg">Large</option>
                  </select>
                </div>

                <div class="property-item">
                  <label class="property-label">
                    <input
                      type="checkbox"
                      checked={$selectedComponent.properties.outline}
                      onchange={(e) => handleInputChange(e, 'outline')}
                    />
                    Outline Style
                  </label>
                </div>

                <div class="property-item">
                  <label class="property-label">
                    <input
                      type="checkbox"
                      checked={$selectedComponent.properties.disabled}
                      onchange={(e) => handleInputChange(e, 'disabled')}
                    />
                    Disabled
                  </label>
                </div>
              </div>
            </div>
          {/if}

          {#if $selectedComponent.name === 'Card'}
            <div class="property-section">
              <h4 class="property-section-title">Card Properties</h4>

              <div class="property-grid">
                <div class="property-item">
                  <label class="property-label">Title</label>
                  <input
                    type="text"
                    class="property-input"
                    value={$selectedComponent.properties.title}
                    oninput={(e) => debouncedInputChange(e, 'title')}
                  />
                </div>

                <div class="property-item">
                  <label class="property-label">Text</label>
                  <textarea
                    class="property-input"
                    rows="3"
                    oninput={(e) => debouncedInputChange(e, 'text')}
                  >{$selectedComponent.properties.text}</textarea>
                </div>

                <div class="property-item">
                  <label class="property-label">Button Text</label>
                  <input
                    type="text"
                    class="property-input"
                    value={$selectedComponent.properties.buttonText}
                    oninput={(e) => debouncedInputChange(e, 'buttonText')}
                  />
                </div>

                <div class="property-item">
                  <label class="property-label">Width</label>
                  <input
                    type="text"
                    class="property-input"
                    value={$selectedComponent.properties.width}
                    oninput={(e) => debouncedInputChange(e, 'width')}
                    placeholder="18rem"
                  />
                </div>
              </div>
            </div>
          {/if}

          {#if $selectedComponent.name === 'Alert'}
            <div class="property-section">
              <h4 class="property-section-title">Alert Properties</h4>

              <div class="property-grid">
                <div class="property-item">
                  <label class="property-label">Text</label>
                  <textarea
                    class="property-input"
                    rows="3"
                    oninput={(e) => debouncedInputChange(e, 'text')}
                  >{$selectedComponent.properties.text}</textarea>
                </div>

                <div class="property-item">
                  <label class="property-label">Variant</label>
                  <select
                    class="property-input"
                    value={$selectedComponent.properties.variant}
                    onchange={(e) => handleInputChange(e, 'variant')}
                  >
                    <option value="primary">Primary</option>
                    <option value="secondary">Secondary</option>
                    <option value="success">Success</option>
                    <option value="danger">Danger</option>
                    <option value="warning">Warning</option>
                    <option value="info">Info</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                <div class="property-item">
                  <label class="property-label">
                    <input
                      type="checkbox"
                      checked={$selectedComponent.properties.dismissible}
                      onchange={(e) => handleInputChange(e, 'dismissible')}
                    />
                    Dismissible
                  </label>
                </div>
              </div>
            </div>
          {/if}
        </div>
      {:else if activeTab === 'css'}
        <div class="p-4">
          <CSSCodeViewer />
        </div>
      {/if}
    {:else}
      <div class="p-8 text-center">
        <div class="text-4xl mb-4 opacity-50">🎨</div>
        <div class="text-sm text-muted">Select a component to view its properties</div>
      </div>
    {/if}
  </div>


</aside>

<style>
  .property-panel {
    background-color: var(--color-surface);
    border-left: 1px solid var(--color-border);
    color: var(--color-text);
  }

  .panel-header {
    padding: 1rem;
    border-bottom: 1px solid var(--color-border);
  }

  .panel-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 0.75rem;
  }

  .panel-tabs {
    display: flex;
    gap: 0.25rem;
  }

  .tab-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    color: var(--color-secondary);
    cursor: pointer;
    transition: all 0.2s;
  }

  .tab-button:hover {
    background-color: var(--color-hover);
  }

  .tab-button.active {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
  }

  .property-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
  }

  .property-section:last-child {
    border-bottom: none;
  }

  .property-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .property-grid {
    display: grid;
    gap: 0.75rem;
  }

  .property-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .property-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--color-secondary);
  }

  .property-input {
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 0.875rem;
    transition: border-color 0.2s;
  }

  .property-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  }

  select.property-input {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  textarea.property-input {
    resize: vertical;
    min-height: 4rem;
  }

  .property-panel.collapsed {
    display: none;
  }

  .text-muted {
    color: var(--color-secondary);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .panel-tabs {
      flex-direction: column;
    }

    .tab-button {
      text-align: center;
    }
  }
</style>
