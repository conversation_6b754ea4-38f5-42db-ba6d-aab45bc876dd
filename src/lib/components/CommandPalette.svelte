<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { getAllShortcuts, formatShortcut } from '../stores/KeyboardStore';
  import type { KeyboardShortcut } from '../stores/KeyboardStore';

  // Component state
  let isOpen = false;
  let searchQuery = '';
  let selectedIndex = 0;
  let searchInput: HTMLInputElement;

  // Available commands
  interface Command {
    id: string;
    name: string;
    description: string;
    category: string;
    shortcut?: KeyboardShortcut;
    action: () => void;
  }

  const commands: Command[] = [
    {
      id: 'undo',
      name: 'Undo',
      description: 'Undo the last action',
      category: 'Edit',
      action: () => {
        // Trigger undo
        console.log('Undo action');
        closeCommandPalette();
      }
    },
    {
      id: 'redo',
      name: 'Redo',
      description: 'Redo the last undone action',
      category: 'Edit',
      action: () => {
        // Trigger redo
        console.log('Redo action');
        closeCommandPalette();
      }
    },
    {
      id: 'copy',
      name: 'Copy',
      description: 'Copy selected component',
      category: 'Edit',
      action: () => {
        // Trigger copy
        console.log('Copy action');
        closeCommandPalette();
      }
    },
    {
      id: 'paste',
      name: 'Paste',
      description: 'Paste component from clipboard',
      category: 'Edit',
      action: () => {
        // Trigger paste
        console.log('Paste action');
        closeCommandPalette();
      }
    },
    {
      id: 'delete',
      name: 'Delete',
      description: 'Delete selected components',
      category: 'Edit',
      action: () => {
        // Trigger delete
        console.log('Delete action');
        closeCommandPalette();
      }
    },
    {
      id: 'save',
      name: 'Save Project',
      description: 'Save the current project',
      category: 'File',
      action: () => {
        // Trigger save
        console.log('Save action');
        closeCommandPalette();
      }
    },
    {
      id: 'export-html',
      name: 'Export HTML',
      description: 'Export project as HTML file',
      category: 'File',
      action: () => {
        // Trigger HTML export
        console.log('Export HTML action');
        closeCommandPalette();
      }
    },
    {
      id: 'export-css',
      name: 'Export CSS',
      description: 'Export project as separate CSS file',
      category: 'File',
      action: () => {
        // Trigger CSS export
        console.log('Export CSS action');
        closeCommandPalette();
      }
    },
    {
      id: 'toggle-grid',
      name: 'Toggle Grid',
      description: 'Show/hide the grid overlay',
      category: 'View',
      action: () => {
        // Toggle grid
        console.log('Toggle grid action');
        closeCommandPalette();
      }
    },
    {
      id: 'toggle-guides',
      name: 'Toggle Guides',
      description: 'Show/hide alignment guides',
      category: 'View',
      action: () => {
        // Toggle guides
        console.log('Toggle guides action');
        closeCommandPalette();
      }
    },
    {
      id: 'zoom-in',
      name: 'Zoom In',
      description: 'Increase canvas zoom level',
      category: 'View',
      action: () => {
        // Zoom in
        console.log('Zoom in action');
        closeCommandPalette();
      }
    },
    {
      id: 'zoom-out',
      name: 'Zoom Out',
      description: 'Decrease canvas zoom level',
      category: 'View',
      action: () => {
        // Zoom out
        console.log('Zoom out action');
        closeCommandPalette();
      }
    },
    {
      id: 'zoom-reset',
      name: 'Reset Zoom',
      description: 'Reset canvas zoom to 100%',
      category: 'View',
      action: () => {
        // Reset zoom
        console.log('Reset zoom action');
        closeCommandPalette();
      }
    }
  ];

  // Add shortcuts to commands
  $: {
    const shortcuts = getAllShortcuts();
    commands.forEach(command => {
      const shortcut = shortcuts.find(s => s.description.toLowerCase() === command.name.toLowerCase());
      if (shortcut) {
        command.shortcut = shortcut;
      }
    });
  }

  // Filtered commands based on search
  $: filteredCommands = commands.filter(command => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      command.name.toLowerCase().includes(query) ||
      command.description.toLowerCase().includes(query) ||
      command.category.toLowerCase().includes(query)
    );
  });

  // Group commands by category
  $: groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, Command[]>);

  // Open command palette
  function openCommandPalette() {
    isOpen = true;
    searchQuery = '';
    selectedIndex = 0;
    setTimeout(() => {
      if (searchInput) {
        searchInput.focus();
      }
    }, 100);
  }

  // Close command palette
  function closeCommandPalette() {
    isOpen = false;
    searchQuery = '';
    selectedIndex = 0;
  }

  // Handle keyboard navigation
  function handleKeydown(event: KeyboardEvent) {
    if (!isOpen) {
      // Open command palette with Ctrl+K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        openCommandPalette();
      }
      return;
    }

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        closeCommandPalette();
        break;
      case 'ArrowDown':
        event.preventDefault();
        selectedIndex = Math.min(selectedIndex + 1, filteredCommands.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        selectedIndex = Math.max(selectedIndex - 1, 0);
        break;
      case 'Enter':
        event.preventDefault();
        if (filteredCommands[selectedIndex]) {
          filteredCommands[selectedIndex].action();
        }
        break;
    }
  }

  // Handle search input
  function handleSearchInput() {
    selectedIndex = 0;
  }

  // Execute command
  function executeCommand(command: Command) {
    command.action();
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  onDestroy(() => {
    document.removeEventListener('keydown', handleKeydown);
  });
</script>

{#if isOpen}
  <!-- Backdrop -->
  <div class="command-palette-backdrop" onclick={closeCommandPalette}></div>

  <!-- Command Palette -->
  <div class="command-palette">
    <!-- Search Input -->
    <div class="search-container">
      <div class="search-icon">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      <input
        bind:this={searchInput}
        bind:value={searchQuery}
        oninput={handleSearchInput}
        type="text"
        placeholder="Type a command or search..."
        class="search-input"
      />
      <div class="search-shortcut">
        <kbd>Ctrl</kbd><kbd>K</kbd>
      </div>
    </div>

    <!-- Commands List -->
    <div class="commands-container">
      {#if Object.keys(groupedCommands).length === 0}
        <div class="no-results">
          <div class="no-results-icon">🔍</div>
          <div class="no-results-text">No commands found</div>
        </div>
      {:else}
        {#each Object.entries(groupedCommands) as [category, categoryCommands]}
          <div class="command-category">
            <div class="category-header">{category}</div>
            {#each categoryCommands as command, index}
              {@const globalIndex = filteredCommands.indexOf(command)}
              <button
                class="command-item"
                class:selected={globalIndex === selectedIndex}
                onclick={() => executeCommand(command)}
              >
                <div class="command-info">
                  <div class="command-name">{command.name}</div>
                  <div class="command-description">{command.description}</div>
                </div>
                {#if command.shortcut}
                  <div class="command-shortcut">
                    {formatShortcut(command.shortcut)}
                  </div>
                {/if}
              </button>
            {/each}
          </div>
        {/each}
      {/if}
    </div>

    <!-- Footer -->
    <div class="command-palette-footer">
      <div class="footer-hint">
        <kbd>↑</kbd><kbd>↓</kbd> to navigate
        <kbd>↵</kbd> to select
        <kbd>esc</kbd> to close
      </div>
    </div>
  </div>
{/if}

<style>
  .command-palette-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }

  .command-palette {
    position: fixed;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 600px;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1001;
    overflow: hidden;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--color-border);
  }

  .search-icon {
    position: absolute;
    left: 1.5rem;
    color: var(--color-secondary);
  }

  .search-input {
    flex: 1;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: none;
    background: transparent;
    color: var(--color-text);
    font-size: 1rem;
    outline: none;
  }

  .search-input::placeholder {
    color: var(--color-secondary);
  }

  .search-shortcut {
    display: flex;
    gap: 0.25rem;
  }

  .commands-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .no-results {
    padding: 3rem 1rem;
    text-align: center;
  }

  .no-results-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .no-results-text {
    color: var(--color-secondary);
    font-size: 0.875rem;
  }

  .command-category {
    padding: 0.5rem 0;
  }

  .category-header {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--color-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .command-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--color-text);
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .command-item:hover,
  .command-item.selected {
    background-color: var(--color-hover);
  }

  .command-info {
    text-align: left;
  }

  .command-name {
    font-weight: 500;
    margin-bottom: 0.125rem;
  }

  .command-description {
    font-size: 0.875rem;
    color: var(--color-secondary);
  }

  .command-shortcut {
    font-size: 0.75rem;
    color: var(--color-secondary);
    font-family: monospace;
  }

  .command-palette-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--color-border);
    background-color: var(--color-background);
  }

  .footer-hint {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--color-secondary);
  }

  kbd {
    padding: 0.125rem 0.375rem;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-family: monospace;
    color: var(--color-text);
  }

  /* Scrollbar styling */
  .commands-container::-webkit-scrollbar {
    width: 0.5rem;
  }

  .commands-container::-webkit-scrollbar-track {
    background-color: var(--color-background);
  }

  .commands-container::-webkit-scrollbar-thumb {
    background-color: var(--color-border);
    border-radius: 0.25rem;
  }

  .commands-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-secondary);
  }
</style>
