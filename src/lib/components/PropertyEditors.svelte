<script lang="ts">
  import { selectedComponent, updateComponentHTML } from './ComponentStore';
  import type { Component } from './ComponentStore';
  import { onMount } from 'svelte';

  // Function to update component
  export let onUpdateComponent: (component: Component) => void;

  // Color picker state
  let colorPickerOpen = false;
  let currentColorProperty = '';

  // Debounce function
  function debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout>;
    return function(...args: Parameters<T>): void {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  // Handle property changes
  function handlePropertyChange(property: string, value: any) {
    if (!$selectedComponent) return;
    
    $selectedComponent.properties[property] = value;
    updateComponentHTML($selectedComponent);
    onUpdateComponent($selectedComponent);
  }

  // Debounced version for text inputs
  const debouncedPropertyChange = debounce(handlePropertyChange, 150);

  // Handle input events
  function handleInputChange(event: Event, property: string, debounced = false) {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
    const value = target.type === 'checkbox' ? (target as HTMLInputElement).checked : target.value;
    
    if (debounced) {
      debouncedPropertyChange(property, value);
    } else {
      handlePropertyChange(property, value);
    }
  }

  // Bootstrap utility classes
  const spacingClasses = ['0', '1', '2', '3', '4', '5', 'auto'];
  const colorVariants = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark'];
  const buttonSizes = ['sm', 'default', 'lg'];
  const displayLevels = [1, 2, 3, 4, 5, 6];

  // Google Fonts (subset for demo)
  const googleFonts = [
    'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana',
    'Roboto', 'Open Sans', 'Lato', 'Montserrat', 'Source Sans Pro'
  ];

  // Unit options
  const units = ['px', '%', 'rem', 'em', 'vh', 'vw'];

  // Color picker component
  function openColorPicker(property: string) {
    currentColorProperty = property;
    colorPickerOpen = true;
  }

  function closeColorPicker() {
    colorPickerOpen = false;
    currentColorProperty = '';
  }

  // Handle color change
  function handleColorChange(event: Event) {
    const target = event.target as HTMLInputElement;
    if (currentColorProperty) {
      handlePropertyChange(currentColorProperty, target.value);
    }
  }
</script>

<!-- Dimensions Editor -->
{#if $selectedComponent}
  <div class="property-section">
    <h4 class="property-section-title">Dimensions</h4>
    
    <div class="property-grid">
      <div class="property-item">
        <label class="property-label">Width</label>
        <div class="dimension-input">
          <input
            type="number"
            class="property-input flex-1"
            value={$selectedComponent.properties.width || ''}
            oninput={(e) => debouncedPropertyChange('width', e.target.value)}
            placeholder="auto"
          />
          <select
            class="unit-select"
            value={$selectedComponent.properties.widthUnit || 'px'}
            onchange={(e) => handlePropertyChange('widthUnit', e.target.value)}
          >
            {#each units as unit}
              <option value={unit}>{unit}</option>
            {/each}
          </select>
        </div>
      </div>

      <div class="property-item">
        <label class="property-label">Height</label>
        <div class="dimension-input">
          <input
            type="number"
            class="property-input flex-1"
            value={$selectedComponent.properties.height || ''}
            oninput={(e) => debouncedPropertyChange('height', e.target.value)}
            placeholder="auto"
          />
          <select
            class="unit-select"
            value={$selectedComponent.properties.heightUnit || 'px'}
            onchange={(e) => handlePropertyChange('heightUnit', e.target.value)}
          >
            {#each units as unit}
              <option value={unit}>{unit}</option>
            {/each}
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacing Editor -->
  <div class="property-section">
    <h4 class="property-section-title">Spacing</h4>
    
    <div class="spacing-editor">
      <div class="spacing-visual">
        <div class="margin-box">
          <span class="spacing-label">Margin</span>
          <input
            type="number"
            class="spacing-input margin-top"
            value={$selectedComponent.properties.marginTop || ''}
            oninput={(e) => handlePropertyChange('marginTop', e.target.value)}
            placeholder="0"
            title="Margin Top"
          />
          <div class="padding-box">
            <span class="spacing-label">Padding</span>
            <input
              type="number"
              class="spacing-input padding-top"
              value={$selectedComponent.properties.paddingTop || ''}
              oninput={(e) => handlePropertyChange('paddingTop', e.target.value)}
              placeholder="0"
              title="Padding Top"
            />
            <div class="content-box">Content</div>
            <input
              type="number"
              class="spacing-input padding-bottom"
              value={$selectedComponent.properties.paddingBottom || ''}
              oninput={(e) => handlePropertyChange('paddingBottom', e.target.value)}
              placeholder="0"
              title="Padding Bottom"
            />
          </div>
          <input
            type="number"
            class="spacing-input margin-bottom"
            value={$selectedComponent.properties.marginBottom || ''}
            oninput={(e) => handlePropertyChange('marginBottom', e.target.value)}
            placeholder="0"
            title="Margin Bottom"
          />
        </div>
        
        <!-- Side inputs -->
        <input
          type="number"
          class="spacing-input margin-left"
          value={$selectedComponent.properties.marginLeft || ''}
          oninput={(e) => handlePropertyChange('marginLeft', e.target.value)}
          placeholder="0"
          title="Margin Left"
        />
        <input
          type="number"
          class="spacing-input padding-left"
          value={$selectedComponent.properties.paddingLeft || ''}
          oninput={(e) => handlePropertyChange('paddingLeft', e.target.value)}
          placeholder="0"
          title="Padding Left"
        />
        <input
          type="number"
          class="spacing-input padding-right"
          value={$selectedComponent.properties.paddingRight || ''}
          oninput={(e) => handlePropertyChange('paddingRight', e.target.value)}
          placeholder="0"
          title="Padding Right"
        />
        <input
          type="number"
          class="spacing-input margin-right"
          value={$selectedComponent.properties.marginRight || ''}
          oninput={(e) => handlePropertyChange('marginRight', e.target.value)}
          placeholder="0"
          title="Margin Right"
        />
      </div>
    </div>
  </div>

  <!-- Typography Editor -->
  <div class="property-section">
    <h4 class="property-section-title">Typography</h4>
    
    <div class="property-grid">
      <div class="property-item">
        <label class="property-label">Font Family</label>
        <select
          class="property-input"
          value={$selectedComponent.properties.fontFamily || 'inherit'}
          onchange={(e) => handlePropertyChange('fontFamily', e.target.value)}
        >
          <option value="inherit">Inherit</option>
          {#each googleFonts as font}
            <option value={font}>{font}</option>
          {/each}
        </select>
      </div>

      <div class="property-item">
        <label class="property-label">Font Size</label>
        <div class="dimension-input">
          <input
            type="number"
            class="property-input flex-1"
            value={$selectedComponent.properties.fontSize || ''}
            oninput={(e) => debouncedPropertyChange('fontSize', e.target.value)}
            placeholder="16"
          />
          <select
            class="unit-select"
            value={$selectedComponent.properties.fontSizeUnit || 'px'}
            onchange={(e) => handlePropertyChange('fontSizeUnit', e.target.value)}
          >
            <option value="px">px</option>
            <option value="rem">rem</option>
            <option value="em">em</option>
            <option value="%">%</option>
          </select>
        </div>
      </div>

      <div class="property-item">
        <label class="property-label">Font Weight</label>
        <select
          class="property-input"
          value={$selectedComponent.properties.fontWeight || 'normal'}
          onchange={(e) => handlePropertyChange('fontWeight', e.target.value)}
        >
          <option value="normal">Normal</option>
          <option value="bold">Bold</option>
          <option value="lighter">Lighter</option>
          <option value="bolder">Bolder</option>
          <option value="100">100</option>
          <option value="200">200</option>
          <option value="300">300</option>
          <option value="400">400</option>
          <option value="500">500</option>
          <option value="600">600</option>
          <option value="700">700</option>
          <option value="800">800</option>
          <option value="900">900</option>
        </select>
      </div>

      <div class="property-item">
        <label class="property-label">Text Color</label>
        <div class="color-input">
          <input
            type="color"
            class="color-picker"
            value={$selectedComponent.properties.color || '#000000'}
            onchange={(e) => handlePropertyChange('color', e.target.value)}
          />
          <input
            type="text"
            class="property-input flex-1"
            value={$selectedComponent.properties.color || ''}
            oninput={(e) => debouncedPropertyChange('color', e.target.value)}
            placeholder="#000000"
          />
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .property-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
  }

  .property-section:last-child {
    border-bottom: none;
  }

  .property-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .property-grid {
    display: grid;
    gap: 0.75rem;
  }

  .property-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .property-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--color-secondary);
  }

  .property-input {
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 0.875rem;
    transition: border-color 0.2s;
  }

  .property-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  }

  .dimension-input {
    display: flex;
    gap: 0.25rem;
  }

  .unit-select {
    width: 4rem;
    padding: 0.5rem 0.25rem;
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 0.75rem;
  }

  .color-input {
    display: flex;
    gap: 0.25rem;
    align-items: center;
  }

  .color-picker {
    width: 2.5rem;
    height: 2.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    cursor: pointer;
  }

  /* Spacing Visual Editor */
  .spacing-editor {
    position: relative;
  }

  .spacing-visual {
    position: relative;
    padding: 2rem;
  }

  .margin-box {
    background-color: #fef3c7;
    border: 2px dashed #f59e0b;
    padding: 1rem;
    position: relative;
  }

  .padding-box {
    background-color: #dcfce7;
    border: 2px dashed #22c55e;
    padding: 1rem;
    position: relative;
  }

  .content-box {
    background-color: #dbeafe;
    border: 2px solid #3b82f6;
    padding: 1rem;
    text-align: center;
    font-size: 0.75rem;
    color: var(--color-secondary);
  }

  .spacing-label {
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    font-size: 0.625rem;
    font-weight: 600;
    color: var(--color-secondary);
    text-transform: uppercase;
  }

  .spacing-input {
    position: absolute;
    width: 2.5rem;
    height: 1.5rem;
    padding: 0.125rem 0.25rem;
    border: 1px solid var(--color-border);
    border-radius: 0.25rem;
    background-color: var(--color-surface);
    color: var(--color-text);
    font-size: 0.75rem;
    text-align: center;
  }

  .margin-top { top: -0.75rem; left: 50%; transform: translateX(-50%); }
  .margin-bottom { bottom: -0.75rem; left: 50%; transform: translateX(-50%); }
  .margin-left { left: -1.25rem; top: 50%; transform: translateY(-50%); }
  .margin-right { right: -1.25rem; top: 50%; transform: translateY(-50%); }

  .padding-top { top: -0.75rem; left: 50%; transform: translateX(-50%); }
  .padding-bottom { bottom: -0.75rem; left: 50%; transform: translateX(-50%); }
  .padding-left { left: -1.25rem; top: 50%; transform: translateY(-50%); }
  .padding-right { right: -1.25rem; top: 50%; transform: translateY(-50%); }
</style>
