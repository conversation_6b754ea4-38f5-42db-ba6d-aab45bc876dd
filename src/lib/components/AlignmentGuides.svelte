<script lang="ts">
  import { currentProject } from '../stores/ProjectStore';
  import type { CanvasComponent } from '../stores/ProjectStore';

  // Guide properties
  export let visible: boolean = true;
  export let canvasWidth: number = 1200;
  export let canvasHeight: number = 800;
  export let components: CanvasComponent[] = [];
  export let activeComponent: CanvasComponent | null = null;

  // Guide types
  interface Guide {
    type: 'vertical' | 'horizontal';
    position: number;
    color: string;
    label?: string;
  }

  // Reactive settings from project
  $: showGuides = visible && $currentProject.settings.showGuides;

  // Guide detection threshold
  const SNAP_THRESHOLD = 5;

  // Active guides
  let activeGuides: Guide[] = [];

  // Calculate alignment guides
  export function calculateGuides(
    draggedComponent: CanvasComponent,
    otherComponents: CanvasComponent[]
  ): Guide[] {
    if (!showGuides || !draggedComponent) return [];

    const guides: Guide[] = [];
    const draggedBounds = getComponentBounds(draggedComponent);

    // Add canvas edge guides
    guides.push(
      { type: 'vertical', position: 0, color: '#ff6b6b', label: 'Left Edge' },
      { type: 'vertical', position: canvasWidth, color: '#ff6b6b', label: 'Right Edge' },
      { type: 'horizontal', position: 0, color: '#ff6b6b', label: 'Top Edge' },
      { type: 'horizontal', position: canvasHeight, color: '#ff6b6b', label: 'Bottom Edge' }
    );

    // Add center guides
    guides.push(
      { type: 'vertical', position: canvasWidth / 2, color: '#4ecdc4', label: 'Center X' },
      { type: 'horizontal', position: canvasHeight / 2, color: '#4ecdc4', label: 'Center Y' }
    );

    // Add component alignment guides
    otherComponents.forEach(component => {
      if (component.id === draggedComponent.id) return;

      const bounds = getComponentBounds(component);

      // Vertical alignment guides
      guides.push(
        { type: 'vertical', position: bounds.left, color: '#45b7d1', label: 'Left Align' },
        { type: 'vertical', position: bounds.right, color: '#45b7d1', label: 'Right Align' },
        { type: 'vertical', position: bounds.centerX, color: '#45b7d1', label: 'Center X Align' }
      );

      // Horizontal alignment guides
      guides.push(
        { type: 'horizontal', position: bounds.top, color: '#45b7d1', label: 'Top Align' },
        { type: 'horizontal', position: bounds.bottom, color: '#45b7d1', label: 'Bottom Align' },
        { type: 'horizontal', position: bounds.centerY, color: '#45b7d1', label: 'Center Y Align' }
      );
    });

    return guides;
  }

  // Get component bounds
  function getComponentBounds(component: CanvasComponent) {
    const left = component.position.x;
    const top = component.position.y;
    const right = left + component.size.width;
    const bottom = top + component.size.height;
    const centerX = left + component.size.width / 2;
    const centerY = top + component.size.height / 2;

    return { left, top, right, bottom, centerX, centerY };
  }

  // Find active guides for a position
  export function findActiveGuides(
    draggedComponent: CanvasComponent,
    otherComponents: CanvasComponent[]
  ): Guide[] {
    const allGuides = calculateGuides(draggedComponent, otherComponents);
    const draggedBounds = getComponentBounds(draggedComponent);
    const active: Guide[] = [];

    allGuides.forEach(guide => {
      let isActive = false;

      if (guide.type === 'vertical') {
        // Check if any vertical edge of dragged component is near this guide
        if (
          Math.abs(draggedBounds.left - guide.position) <= SNAP_THRESHOLD ||
          Math.abs(draggedBounds.right - guide.position) <= SNAP_THRESHOLD ||
          Math.abs(draggedBounds.centerX - guide.position) <= SNAP_THRESHOLD
        ) {
          isActive = true;
        }
      } else {
        // Check if any horizontal edge of dragged component is near this guide
        if (
          Math.abs(draggedBounds.top - guide.position) <= SNAP_THRESHOLD ||
          Math.abs(draggedBounds.bottom - guide.position) <= SNAP_THRESHOLD ||
          Math.abs(draggedBounds.centerY - guide.position) <= SNAP_THRESHOLD
        ) {
          isActive = true;
        }
      }

      if (isActive) {
        active.push(guide);
      }
    });

    return active;
  }

  // Snap position to guides
  export function snapToGuides(
    position: { x: number; y: number },
    size: { width: number; height: number },
    otherComponents: CanvasComponent[]
  ): { x: number; y: number } {
    if (!showGuides) return position;

    const tempComponent: CanvasComponent = {
      id: 'temp',
      componentId: 0,
      html: '',
      position,
      size,
      children: [],
      zIndex: 0,
      locked: false,
      visible: true
    };

    const guides = calculateGuides(tempComponent, otherComponents);
    let snappedX = position.x;
    let snappedY = position.y;

    // Find closest vertical guide
    let closestVerticalDistance = SNAP_THRESHOLD + 1;
    guides.filter(g => g.type === 'vertical').forEach(guide => {
      const leftDistance = Math.abs(position.x - guide.position);
      const rightDistance = Math.abs((position.x + size.width) - guide.position);
      const centerDistance = Math.abs((position.x + size.width / 2) - guide.position);

      if (leftDistance <= SNAP_THRESHOLD && leftDistance < closestVerticalDistance) {
        snappedX = guide.position;
        closestVerticalDistance = leftDistance;
      } else if (rightDistance <= SNAP_THRESHOLD && rightDistance < closestVerticalDistance) {
        snappedX = guide.position - size.width;
        closestVerticalDistance = rightDistance;
      } else if (centerDistance <= SNAP_THRESHOLD && centerDistance < closestVerticalDistance) {
        snappedX = guide.position - size.width / 2;
        closestVerticalDistance = centerDistance;
      }
    });

    // Find closest horizontal guide
    let closestHorizontalDistance = SNAP_THRESHOLD + 1;
    guides.filter(g => g.type === 'horizontal').forEach(guide => {
      const topDistance = Math.abs(position.y - guide.position);
      const bottomDistance = Math.abs((position.y + size.height) - guide.position);
      const centerDistance = Math.abs((position.y + size.height / 2) - guide.position);

      if (topDistance <= SNAP_THRESHOLD && topDistance < closestHorizontalDistance) {
        snappedY = guide.position;
        closestHorizontalDistance = topDistance;
      } else if (bottomDistance <= SNAP_THRESHOLD && bottomDistance < closestHorizontalDistance) {
        snappedY = guide.position - size.height;
        closestHorizontalDistance = bottomDistance;
      } else if (centerDistance <= SNAP_THRESHOLD && centerDistance < closestHorizontalDistance) {
        snappedY = guide.position - size.height / 2;
        closestHorizontalDistance = centerDistance;
      }
    });

    return { x: snappedX, y: snappedY };
  }

  // Update active guides when component moves
  export function updateActiveGuides(
    draggedComponent: CanvasComponent | null,
    otherComponents: CanvasComponent[]
  ): void {
    if (!draggedComponent || !showGuides) {
      activeGuides = [];
      return;
    }

    activeGuides = findActiveGuides(draggedComponent, otherComponents);
  }

  // Clear active guides
  export function clearActiveGuides(): void {
    activeGuides = [];
  }
</script>

{#if showGuides && activeGuides.length > 0}
  <div class="alignment-guides" style="width: {canvasWidth}px; height: {canvasHeight}px;">
    {#each activeGuides as guide}
      <div
        class="guide-line {guide.type}"
        style="
          {guide.type === 'vertical' ? `left: ${guide.position}px;` : `top: ${guide.position}px;`}
          border-color: {guide.color};
        "
      >
        {#if guide.label}
          <div class="guide-label" style="color: {guide.color};">
            {guide.label}
          </div>
        {/if}
      </div>
    {/each}
  </div>
{/if}

<style>
  .alignment-guides {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 10;
    overflow: hidden;
  }

  .guide-line {
    position: absolute;
    pointer-events: none;
    z-index: 10;
  }

  .guide-line.vertical {
    width: 0;
    height: 100%;
    border-left: 1px dashed;
    top: 0;
  }

  .guide-line.horizontal {
    height: 0;
    width: 100%;
    border-top: 1px dashed;
    left: 0;
  }

  .guide-label {
    position: absolute;
    font-size: 10px;
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
    font-family: monospace;
  }

  .guide-line.vertical .guide-label {
    left: 5px;
    top: 10px;
    transform: rotate(-90deg);
    transform-origin: left top;
  }

  .guide-line.horizontal .guide-label {
    top: 5px;
    left: 10px;
  }

  /* Animation for guide appearance */
  .guide-line {
    animation: guideAppear 0.2s ease-out;
  }

  @keyframes guideAppear {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Dark mode adjustments */
  :global(.dark) .guide-label {
    background-color: rgba(255, 255, 255, 0.9);
    color: black;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .guide-label {
      font-size: 8px;
      padding: 1px 4px;
    }
  }
</style>
