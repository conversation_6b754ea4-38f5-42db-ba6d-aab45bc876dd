<script lang="ts">
  import { currentProject } from '../stores/ProjectStore';

  // Grid properties
  export let visible: boolean = true;
  export let gridSize: number = 8;
  export let snapToGrid: boolean = true;

  // Canvas dimensions
  export let canvasWidth: number = 1200;
  export let canvasHeight: number = 800;

  // Reactive grid settings from project
  $: gridEnabled = $currentProject.settings.gridEnabled;
  $: projectGridSize = $currentProject.settings.gridSize;
  $: projectSnapToGrid = $currentProject.settings.snapToGrid;

  // Use project settings if available
  $: effectiveGridSize = projectGridSize || gridSize;
  $: effectiveVisible = visible && gridEnabled;
  $: effectiveSnapToGrid = snapToGrid && projectSnapToGrid;

  // Calculate grid lines
  $: verticalLines = Math.ceil(canvasWidth / effectiveGridSize);
  $: horizontalLines = Math.ceil(canvasHeight / effectiveGridSize);

  // Generate grid line positions
  $: verticalPositions = Array.from({ length: verticalLines + 1 }, (_, i) => i * effectiveGridSize);
  $: horizontalPositions = Array.from({ length: horizontalLines + 1 }, (_, i) => i * effectiveGridSize);

  // Snap position to grid
  export function snapToGridPosition(x: number, y: number): { x: number; y: number } {
    if (!effectiveSnapToGrid) {
      return { x, y };
    }

    const snappedX = Math.round(x / effectiveGridSize) * effectiveGridSize;
    const snappedY = Math.round(y / effectiveGridSize) * effectiveGridSize;

    return { x: snappedX, y: snappedY };
  }

  // Check if position is on grid
  export function isOnGrid(x: number, y: number): boolean {
    return (
      x % effectiveGridSize === 0 &&
      y % effectiveGridSize === 0
    );
  }

  // Get nearest grid position
  export function getNearestGridPosition(x: number, y: number): { x: number; y: number } {
    const nearestX = Math.round(x / effectiveGridSize) * effectiveGridSize;
    const nearestY = Math.round(y / effectiveGridSize) * effectiveGridSize;
    return { x: nearestX, y: nearestY };
  }

  // Get grid intersection points near a position
  export function getNearbyGridPoints(x: number, y: number, radius: number = 20): Array<{ x: number; y: number }> {
    const points: Array<{ x: number; y: number }> = [];
    
    const startX = Math.floor((x - radius) / effectiveGridSize) * effectiveGridSize;
    const endX = Math.ceil((x + radius) / effectiveGridSize) * effectiveGridSize;
    const startY = Math.floor((y - radius) / effectiveGridSize) * effectiveGridSize;
    const endY = Math.ceil((y + radius) / effectiveGridSize) * effectiveGridSize;

    for (let gridX = startX; gridX <= endX; gridX += effectiveGridSize) {
      for (let gridY = startY; gridY <= endY; gridY += effectiveGridSize) {
        const distance = Math.sqrt((gridX - x) ** 2 + (gridY - y) ** 2);
        if (distance <= radius) {
          points.push({ x: gridX, y: gridY });
        }
      }
    }

    return points;
  }
</script>

{#if effectiveVisible}
  <div class="canvas-grid" style="width: {canvasWidth}px; height: {canvasHeight}px;">
    <!-- Vertical grid lines -->
    {#each verticalPositions as x}
      <div
        class="grid-line vertical"
        class:major={x % (effectiveGridSize * 5) === 0}
        style="left: {x}px;"
      ></div>
    {/each}

    <!-- Horizontal grid lines -->
    {#each horizontalPositions as y}
      <div
        class="grid-line horizontal"
        class:major={y % (effectiveGridSize * 5) === 0}
        style="top: {y}px;"
      ></div>
    {/each}

    <!-- Grid dots at intersections (for smaller grid sizes) -->
    {#if effectiveGridSize <= 16}
      {#each verticalPositions as x}
        {#each horizontalPositions as y}
          <div
            class="grid-dot"
            class:major={x % (effectiveGridSize * 5) === 0 && y % (effectiveGridSize * 5) === 0}
            style="left: {x}px; top: {y}px;"
          ></div>
        {/each}
      {/each}
    {/if}

    <!-- Grid info overlay -->
    <div class="grid-info">
      <span class="grid-size">Grid: {effectiveGridSize}px</span>
      {#if effectiveSnapToGrid}
        <span class="snap-indicator">📌 Snap</span>
      {/if}
    </div>
  </div>
{/if}

<style>
  .canvas-grid {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
  }

  .grid-line {
    position: absolute;
    background-color: rgba(0, 123, 255, 0.1);
    pointer-events: none;
  }

  .grid-line.vertical {
    width: 1px;
    height: 100%;
    top: 0;
  }

  .grid-line.horizontal {
    height: 1px;
    width: 100%;
    left: 0;
  }

  .grid-line.major {
    background-color: rgba(0, 123, 255, 0.2);
  }

  .grid-dot {
    position: absolute;
    width: 2px;
    height: 2px;
    background-color: rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }

  .grid-dot.major {
    width: 3px;
    height: 3px;
    background-color: rgba(0, 123, 255, 0.5);
  }

  .grid-info {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
    pointer-events: none;
  }

  .grid-size {
    opacity: 0.8;
  }

  .snap-indicator {
    color: #28a745;
    font-weight: bold;
  }

  /* Dark mode adjustments */
  :global(.dark) .grid-line {
    background-color: rgba(255, 255, 255, 0.1);
  }

  :global(.dark) .grid-line.major {
    background-color: rgba(255, 255, 255, 0.2);
  }

  :global(.dark) .grid-dot {
    background-color: rgba(255, 255, 255, 0.3);
  }

  :global(.dark) .grid-dot.major {
    background-color: rgba(255, 255, 255, 0.5);
  }

  :global(.dark) .grid-info {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }

  /* Animation for grid visibility changes */
  .canvas-grid {
    transition: opacity 0.2s ease-in-out;
  }

  /* Responsive grid visibility */
  @media (max-width: 768px) {
    .grid-info {
      font-size: 10px;
      padding: 2px 6px;
    }
  }
</style>
