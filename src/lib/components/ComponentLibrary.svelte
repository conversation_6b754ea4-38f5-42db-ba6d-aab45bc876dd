<script lang="ts">
  import {
    components,
    selectedComponent,
    filteredComponents,
    categories,
    searchQuery,
    selectedCategory
  } from './ComponentStore';
  import { handleDragStart, handleDragEnd } from './DragAndDrop';
  import type { Component } from './ComponentStore';
  import { onMount } from 'svelte';

  // Function to select a component
  export let onSelectComponent: (component: Component) => void;

  // Collapsed state
  export let collapsed: boolean = false;

  // Search input element for focus management
  let searchInput: HTMLInputElement;

  // Debounced search function
  let searchTimeout: NodeJS.Timeout;
  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      searchQuery.set(target.value);
    }, 300);
  }

  // Clear search
  function clearSearch() {
    searchQuery.set('');
    if (searchInput) {
      searchInput.value = '';
      searchInput.focus();
    }
  }

  // Handle category selection
  function selectCategory(category: string) {
    selectedCategory.set(category);
  }

  // Component preview state
  let hoveredComponent: Component | null = null;
  let previewTimeout: NodeJS.Timeout;

  function showPreview(component: Component) {
    clearTimeout(previewTimeout);
    previewTimeout = setTimeout(() => {
      hoveredComponent = component;
    }, 500);
  }

  function hidePreview() {
    clearTimeout(previewTimeout);
    hoveredComponent = null;
  }

  // Keyboard navigation
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      clearSearch();
    } else if (event.ctrlKey && event.key === 'f') {
      event.preventDefault();
      if (searchInput) {
        searchInput.focus();
      }
    }
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeydown);
    return () => {
      document.removeEventListener('keydown', handleKeydown);
    };
  });
</script>

<aside class="w-full h-full flex flex-col component-library" class:collapsed>
  <!-- Header with Search -->
  <div class="library-header p-4">
    <h2 class="text-lg font-semibold mb-3">Components</h2>

    <!-- Search Bar -->
    <div class="search-container relative mb-3">
      <input
        bind:this={searchInput}
        type="text"
        placeholder="Search components... (Ctrl+F)"
        class="search-input w-full pl-8 pr-8 py-2 text-sm rounded-lg"
        oninput={handleSearchInput}
      />
      <div class="search-icon absolute left-2 top-1/2 transform -translate-y-1/2">
        <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      {#if $searchQuery}
        <button
          class="clear-search absolute right-2 top-1/2 transform -translate-y-1/2 opacity-50 hover:opacity-100"
          onclick={clearSearch}
          title="Clear search"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      {/if}
    </div>

    <!-- Category Filter -->
    <div class="category-filter">
      <div class="flex flex-wrap gap-1">
        {#each $categories as category}
          <button
            class="category-btn px-2 py-1 text-xs rounded transition-colors"
            class:active={$selectedCategory === category}
            onclick={() => selectCategory(category)}
          >
            {category}
          </button>
        {/each}
      </div>
    </div>
  </div>

  <!-- Components List -->
  <div class="overflow-y-auto flex-1 p-2">
    {#if $filteredComponents.length === 0}
      <div class="text-center text-muted py-8">
        <div class="text-2xl mb-2">🔍</div>
        <div class="text-sm">No components found</div>
        {#if $searchQuery}
          <button class="text-xs text-primary mt-2 underline" onclick={clearSearch}>
            Clear search
          </button>
        {/if}
      </div>
    {:else}
      {#each Object.entries(
        $filteredComponents.reduce((acc, component) => {
          if (!acc[component.category]) {
            acc[component.category] = [];
          }
          acc[component.category].push(component);
          return acc;
        }, {} as Record<string, Component[]>)
      ) as [category, categoryComponents]}
        <div class="mb-4">
          <h3 class="font-semibold mb-2 category-title">
            {category} ({categoryComponents.length})
          </h3>
          <ul class="pl-2">
            {#each categoryComponents as component}
              <button
                type="button"
                class="p-2 cursor-pointer rounded mb-1 w-full text-left component-item transition-all duration-200"
                class:selected={$selectedComponent?.id === component.id}
                onclick={() => onSelectComponent(component)}
                onkeydown={(e) => e.key === 'Enter' && onSelectComponent(component)}
                onmouseenter={() => showPreview(component)}
                onmouseleave={hidePreview}
                draggable="true"
                ondragstart={(e) => handleDragStart(e, component)}
                ondragend={handleDragEnd}
              >
                <div class="flex items-center">
                  <span class="component-icon mr-2 text-xl">{component.icon}</span>
                  <span class="flex-1">{component.name}</span>
                  <div class="drag-handle opacity-30 hover:opacity-60">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                    </svg>
                  </div>
                </div>
                <div class="component-preview mt-1 p-1 rounded text-xs overflow-hidden h-8">
                  <div class="preview-content" style="transform: scale(0.7); transform-origin: top left;">
                    {@html component.html}
                  </div>
                </div>
              </button>
            {/each}
          </ul>
        </div>
      {/each}
    {/if}
  </div>

  <!-- Component Hover Preview -->
  {#if hoveredComponent}
    <div class="hover-preview fixed z-50 bg-white border shadow-lg rounded-lg p-4 pointer-events-none">
      <div class="text-sm font-medium mb-2">{hoveredComponent.name}</div>
      <div class="preview-large" style="width: 200px; height: 150px; overflow: hidden;">
        {@html hoveredComponent.html}
      </div>
    </div>
  {/if}
</aside>

<style>
  .component-library {
    background-color: var(--color-surface);
    border-right: 1px solid var(--color-border);
    color: var(--color-text);
  }

  .library-header {
    border-bottom: 1px solid var(--color-border);
  }

  .search-input {
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    color: var(--color-text);
    transition: border-color 0.2s;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  }

  .category-btn {
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    color: var(--color-secondary);
  }

  .category-btn:hover {
    background-color: var(--color-hover);
  }

  .category-btn.active {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
  }

  .category-title {
    color: var(--color-secondary);
  }

  .component-item {
    transition: all 0.2s;
    border: 1px solid transparent;
  }

  .component-item:hover {
    background-color: var(--color-hover);
    border-color: var(--color-border);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .component-item.selected {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
  }

  .component-preview {
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    color: var(--color-secondary);
    position: relative;
    overflow: hidden;
  }

  .preview-content {
    pointer-events: none;
    user-select: none;
  }

  .drag-handle {
    transition: opacity 0.2s;
  }

  .hover-preview {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text);
    max-width: 250px;
    z-index: 1000;
  }

  .component-library.collapsed {
    display: none;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .category-filter {
      overflow-x: auto;
    }

    .category-btn {
      white-space: nowrap;
    }

    .hover-preview {
      display: none;
    }
  }
</style>
