<script lang="ts">
  import { selectedComponent } from './ComponentStore';
  import { onMount, afterUpdate } from 'svelte';
  import Prism from 'prismjs';
  import 'prismjs/components/prism-css';
  import 'prismjs/themes/prism.css';

  // CSS code state
  let cssCode = '';
  let customCSS = '';
  let showCustomEditor = false;
  let codeElement: HTMLElement;

  // Generate CSS from component properties
  function generateCSS(component: any): string {
    if (!component) return '';

    const props = component.properties;
    const selector = `#${props.id}`;
    let css = `${selector} {\n`;

    // Dimensions
    if (props.width) {
      css += `  width: ${props.width}${props.widthUnit || 'px'};\n`;
    }
    if (props.height) {
      css += `  height: ${props.height}${props.heightUnit || 'px'};\n`;
    }

    // Spacing
    if (props.marginTop) css += `  margin-top: ${props.marginTop}px;\n`;
    if (props.marginRight) css += `  margin-right: ${props.marginRight}px;\n`;
    if (props.marginBottom) css += `  margin-bottom: ${props.marginBottom}px;\n`;
    if (props.marginLeft) css += `  margin-left: ${props.marginLeft}px;\n`;

    if (props.paddingTop) css += `  padding-top: ${props.paddingTop}px;\n`;
    if (props.paddingRight) css += `  padding-right: ${props.paddingRight}px;\n`;
    if (props.paddingBottom) css += `  padding-bottom: ${props.paddingBottom}px;\n`;
    if (props.paddingLeft) css += `  padding-left: ${props.paddingLeft}px;\n`;

    // Typography
    if (props.fontFamily && props.fontFamily !== 'inherit') {
      css += `  font-family: "${props.fontFamily}", sans-serif;\n`;
    }
    if (props.fontSize) {
      css += `  font-size: ${props.fontSize}${props.fontSizeUnit || 'px'};\n`;
    }
    if (props.fontWeight && props.fontWeight !== 'normal') {
      css += `  font-weight: ${props.fontWeight};\n`;
    }
    if (props.color) {
      css += `  color: ${props.color};\n`;
    }

    // Background
    if (props.backgroundColor) {
      css += `  background-color: ${props.backgroundColor};\n`;
    }
    if (props.backgroundImage) {
      css += `  background-image: url("${props.backgroundImage}");\n`;
    }
    if (props.backgroundSize) {
      css += `  background-size: ${props.backgroundSize};\n`;
    }
    if (props.backgroundPosition) {
      css += `  background-position: ${props.backgroundPosition};\n`;
    }
    if (props.backgroundRepeat) {
      css += `  background-repeat: ${props.backgroundRepeat};\n`;
    }

    // Border
    if (props.borderWidth) {
      css += `  border-width: ${props.borderWidth}px;\n`;
    }
    if (props.borderStyle) {
      css += `  border-style: ${props.borderStyle};\n`;
    }
    if (props.borderColor) {
      css += `  border-color: ${props.borderColor};\n`;
    }
    if (props.borderRadius) {
      css += `  border-radius: ${props.borderRadius}px;\n`;
    }

    // Position
    if (props.position) {
      css += `  position: ${props.position};\n`;
    }
    if (props.top) css += `  top: ${props.top}px;\n`;
    if (props.right) css += `  right: ${props.right}px;\n`;
    if (props.bottom) css += `  bottom: ${props.bottom}px;\n`;
    if (props.left) css += `  left: ${props.left}px;\n`;
    if (props.zIndex) css += `  z-index: ${props.zIndex};\n`;

    // Display and layout
    if (props.display) {
      css += `  display: ${props.display};\n`;
    }
    if (props.flexDirection) {
      css += `  flex-direction: ${props.flexDirection};\n`;
    }
    if (props.justifyContent) {
      css += `  justify-content: ${props.justifyContent};\n`;
    }
    if (props.alignItems) {
      css += `  align-items: ${props.alignItems};\n`;
    }
    if (props.flexWrap) {
      css += `  flex-wrap: ${props.flexWrap};\n`;
    }

    css += '}';

    // Add custom CSS if present
    if (customCSS.trim()) {
      css += '\n\n/* Custom CSS */\n' + customCSS;
    }

    return css;
  }

  // Update CSS when component changes
  $: if ($selectedComponent) {
    cssCode = generateCSS($selectedComponent);
  }

  // Highlight code after updates
  afterUpdate(() => {
    if (codeElement) {
      Prism.highlightElement(codeElement);
    }
  });

  // Copy CSS to clipboard
  async function copyCSSToClipboard() {
    try {
      await navigator.clipboard.writeText(cssCode);
      // Show success feedback
      const button = document.querySelector('.copy-button');
      if (button) {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy CSS';
        }, 2000);
      }
    } catch (err) {
      console.error('Failed to copy CSS:', err);
    }
  }

  // Handle custom CSS changes
  function handleCustomCSSChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    customCSS = target.value;
    cssCode = generateCSS($selectedComponent);
  }

  // Apply custom CSS to component
  function applyCustomCSS() {
    if ($selectedComponent && customCSS.trim()) {
      // This would need to be integrated with the component update system
      // For now, just update the CSS display
      cssCode = generateCSS($selectedComponent);
    }
  }

  // Toggle custom CSS editor
  function toggleCustomEditor() {
    showCustomEditor = !showCustomEditor;
  }

  onMount(() => {
    // Initialize Prism
    Prism.manual = true;
  });
</script>

<div class="css-viewer">
  <div class="css-header">
    <h4 class="css-title">Generated CSS</h4>
    <div class="css-actions">
      <button
        class="action-button"
        onclick={toggleCustomEditor}
        title="Toggle custom CSS editor"
      >
        {showCustomEditor ? 'Hide Editor' : 'Custom CSS'}
      </button>
      <button
        class="action-button copy-button"
        onclick={copyCSSToClipboard}
        title="Copy CSS to clipboard"
      >
        Copy CSS
      </button>
    </div>
  </div>

  {#if showCustomEditor}
    <div class="custom-css-editor">
      <label class="editor-label">Custom CSS</label>
      <textarea
        class="custom-css-textarea"
        placeholder="/* Add your custom CSS here */&#10;.my-class {&#10;  /* styles */&#10;}"
        value={customCSS}
        oninput={handleCustomCSSChange}
        rows="6"
      ></textarea>
      <button class="apply-button" onclick={applyCustomCSS}>
        Apply CSS
      </button>
    </div>
  {/if}

  <div class="css-code-container">
    <pre class="css-code"><code bind:this={codeElement} class="language-css">{cssCode}</code></pre>
  </div>
</div>

<style>
  .css-viewer {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .css-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--color-background);
    border-bottom: 1px solid var(--color-border);
  }

  .css-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-text);
    margin: 0;
  }

  .css-actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    background-color: var(--color-primary);
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .action-button:hover {
    background-color: var(--color-primary);
    opacity: 0.9;
  }

  .custom-css-editor {
    padding: 1rem;
    background-color: var(--color-background);
    border-bottom: 1px solid var(--color-border);
  }

  .editor-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
  }

  .custom-css-textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    background-color: var(--color-surface);
    color: var(--color-text);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    line-height: 1.4;
    resize: vertical;
    min-height: 6rem;
  }

  .custom-css-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
  }

  .apply-button {
    margin-top: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    background-color: var(--color-accent);
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .apply-button:hover {
    opacity: 0.9;
  }

  .css-code-container {
    max-height: 20rem;
    overflow-y: auto;
  }

  .css-code {
    margin: 0;
    padding: 1rem;
    background-color: var(--color-surface);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /* Override Prism theme for dark mode compatibility */
  :global(.token.selector) {
    color: #22c55e;
  }

  :global(.token.property) {
    color: #3b82f6;
  }

  :global(.token.string) {
    color: #f59e0b;
  }

  :global(.token.number) {
    color: #ef4444;
  }

  :global(.token.punctuation) {
    color: var(--color-secondary);
  }

  /* Scrollbar styling */
  .css-code-container::-webkit-scrollbar {
    width: 0.5rem;
  }

  .css-code-container::-webkit-scrollbar-track {
    background-color: var(--color-background);
  }

  .css-code-container::-webkit-scrollbar-thumb {
    background-color: var(--color-border);
    border-radius: 0.25rem;
  }

  .css-code-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-secondary);
  }
</style>
