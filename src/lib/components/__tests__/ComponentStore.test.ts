import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { 
  components, 
  selectedComponent, 
  filteredComponents, 
  searchQuery, 
  selectedCategory,
  updateComponentHTML 
} from '../ComponentStore';

describe('ComponentStore', () => {
  beforeEach(() => {
    // Reset stores before each test
    selectedComponent.set(null);
    searchQuery.set('');
    selectedCategory.set('All');
  });

  it('should have initial components loaded', () => {
    const $components = get(components);
    expect($components.length).toBeGreaterThan(0);
    expect($components[0]).toHaveProperty('id');
    expect($components[0]).toHaveProperty('name');
    expect($components[0]).toHaveProperty('category');
    expect($components[0]).toHaveProperty('html');
    expect($components[0]).toHaveProperty('properties');
  });

  it('should filter components by search query', () => {
    searchQuery.set('button');
    const $filteredComponents = get(filteredComponents);
    
    expect($filteredComponents.length).toBeGreaterThan(0);
    expect($filteredComponents.every(c => 
      c.name.toLowerCase().includes('button') || 
      c.category.toLowerCase().includes('button')
    )).toBe(true);
  });

  it('should filter components by category', () => {
    selectedCategory.set('Layout');
    const $filteredComponents = get(filteredComponents);
    
    expect($filteredComponents.length).toBeGreaterThan(0);
    expect($filteredComponents.every(c => c.category === 'Layout')).toBe(true);
  });

  it('should update component HTML based on properties', () => {
    const $components = get(components);
    const buttonComponent = $components.find(c => c.name === 'Button');
    
    if (buttonComponent) {
      buttonComponent.properties.text = 'Test Button';
      buttonComponent.properties.variant = 'success';
      
      updateComponentHTML(buttonComponent);
      
      expect(buttonComponent.html).toContain('Test Button');
      expect(buttonComponent.html).toContain('btn-success');
    }
  });

  it('should handle selected component', () => {
    const $components = get(components);
    const firstComponent = $components[0];
    
    selectedComponent.set(firstComponent);
    const $selectedComponent = get(selectedComponent);
    
    expect($selectedComponent).toEqual(firstComponent);
  });

  it('should return empty array when search has no matches', () => {
    searchQuery.set('nonexistentcomponent');
    const $filteredComponents = get(filteredComponents);
    
    expect($filteredComponents).toHaveLength(0);
  });

  it('should combine search and category filters', () => {
    searchQuery.set('container');
    selectedCategory.set('Layout');
    const $filteredComponents = get(filteredComponents);
    
    expect($filteredComponents.every(c => 
      c.category === 'Layout' && 
      (c.name.toLowerCase().includes('container') || 
       c.category.toLowerCase().includes('container'))
    )).toBe(true);
  });
});
