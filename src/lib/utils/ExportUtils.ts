import type { Project, CanvasComponent } from '../stores/ProjectStore';

// Export options interface
export interface ExportOptions {
  includeBootstrapCDN: boolean;
  separateCSS: boolean;
  minifyOutput: boolean;
  includeComments: boolean;
  responsive: boolean;
  includeJavaScript: boolean;
}

// Default export options
export const defaultExportOptions: ExportOptions = {
  includeBootstrapCDN: true,
  separateCSS: false,
  minifyOutput: false,
  includeComments: true,
  responsive: true,
  includeJavaScript: true
};

// Generate HTML from project
export function generateHTML(project: Project, options: ExportOptions = defaultExportOptions): string {
  const { canvasComponents, settings } = project;
  
  let html = '';
  
  // Add DOCTYPE and opening HTML tag
  html += '<!DOCTYPE html>\n';
  html += '<html lang="en">\n';
  
  // Generate head section
  html += generateHead(project, options);
  
  // Generate body section
  html += '<body>\n';
  
  if (options.includeComments) {
    html += `  <!-- Generated by Evolve UI - ${new Date().toISOString()} -->\n`;
  }
  
  // Add container
  html += '  <div class="container-fluid">\n';
  
  // Generate components
  html += generateComponents(canvasComponents, options);
  
  html += '  </div>\n';
  
  // Add JavaScript if needed
  if (options.includeJavaScript) {
    html += generateJavaScript(options);
  }
  
  html += '</body>\n';
  html += '</html>';
  
  return options.minifyOutput ? minifyHTML(html) : html;
}

// Generate CSS from project
export function generateCSS(project: Project, options: ExportOptions = defaultExportOptions): string {
  const { canvasComponents, settings } = project;
  
  let css = '';
  
  if (options.includeComments) {
    css += `/* Generated by Evolve UI - ${new Date().toISOString()} */\n\n`;
  }
  
  // Add base styles
  css += generateBaseStyles(options);
  
  // Add component styles
  css += generateComponentStyles(canvasComponents, options);
  
  // Add responsive styles
  if (options.responsive) {
    css += generateResponsiveStyles(canvasComponents, options);
  }
  
  return options.minifyOutput ? minifyCSS(css) : css;
}

// Generate head section
function generateHead(project: Project, options: ExportOptions): string {
  let head = '<head>\n';
  head += '  <meta charset="UTF-8">\n';
  head += '  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
  head += `  <title>${escapeHTML(project.name)}</title>\n`;
  
  if (options.includeComments && project.description) {
    head += `  <!-- ${escapeHTML(project.description)} -->\n`;
  }
  
  // Add Bootstrap CSS
  if (options.includeBootstrapCDN) {
    head += '  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">\n';
  }
  
  // Add custom CSS
  if (!options.separateCSS) {
    head += '  <style>\n';
    head += generateCSS(project, { ...options, includeComments: false }).split('\n').map(line => `    ${line}`).join('\n');
    head += '\n  </style>\n';
  } else {
    head += '  <link rel="stylesheet" href="styles.css">\n';
  }
  
  head += '</head>\n';
  return head;
}

// Generate components HTML
function generateComponents(components: CanvasComponent[], options: ExportOptions): string {
  let html = '';
  
  // Sort components by z-index
  const sortedComponents = [...components].sort((a, b) => a.zIndex - b.zIndex);
  
  sortedComponents.forEach(component => {
    if (!component.visible) return;
    
    if (options.includeComments) {
      html += `    <!-- Component: ${component.id} -->\n`;
    }
    
    html += `    <div class="evolve-component" id="${component.id}" style="position: absolute; left: ${component.position.x}px; top: ${component.position.y}px; width: ${component.size.width}px; height: ${component.size.height}px; z-index: ${component.zIndex};">\n`;
    
    // Add component HTML with proper indentation
    const componentHTML = component.html.split('\n').map(line => `      ${line}`).join('\n');
    html += componentHTML + '\n';
    
    html += '    </div>\n';
  });
  
  return html;
}

// Generate base styles
function generateBaseStyles(options: ExportOptions): string {
  let css = '';
  
  if (options.includeComments) {
    css += '/* Base Styles */\n';
  }
  
  css += 'body {\n';
  css += '  margin: 0;\n';
  css += '  padding: 0;\n';
  css += '  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;\n';
  css += '}\n\n';
  
  css += '.evolve-component {\n';
  css += '  position: absolute;\n';
  css += '  box-sizing: border-box;\n';
  css += '}\n\n';
  
  return css;
}

// Generate component-specific styles
function generateComponentStyles(components: CanvasComponent[], options: ExportOptions): string {
  let css = '';
  
  if (options.includeComments) {
    css += '/* Component Styles */\n';
  }
  
  components.forEach(component => {
    if (!component.visible) return;
    
    css += `#${component.id} {\n`;
    css += `  /* Component: ${component.id} */\n`;
    css += '}\n\n';
  });
  
  return css;
}

// Generate responsive styles
function generateResponsiveStyles(components: CanvasComponent[], options: ExportOptions): string {
  let css = '';
  
  if (options.includeComments) {
    css += '/* Responsive Styles */\n';
  }
  
  // Mobile styles
  css += '@media (max-width: 768px) {\n';
  css += '  .evolve-component {\n';
  css += '    position: relative !important;\n';
  css += '    left: auto !important;\n';
  css += '    top: auto !important;\n';
  css += '    width: 100% !important;\n';
  css += '    height: auto !important;\n';
  css += '    margin-bottom: 1rem;\n';
  css += '  }\n';
  css += '}\n\n';
  
  // Tablet styles
  css += '@media (min-width: 769px) and (max-width: 1024px) {\n';
  css += '  .evolve-component {\n';
  css += '    /* Tablet-specific adjustments */\n';
  css += '  }\n';
  css += '}\n\n';
  
  return css;
}

// Generate JavaScript
function generateJavaScript(options: ExportOptions): string {
  let js = '';
  
  if (options.includeBootstrapCDN) {
    js += '  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>\n';
  }
  
  if (options.includeComments) {
    js += '  <!-- Custom JavaScript -->\n';
  }
  
  js += '  <script>\n';
  js += '    // Initialize Bootstrap components\n';
  js += '    document.addEventListener("DOMContentLoaded", function() {\n';
  js += '      // Initialize tooltips\n';
  js += '      var tooltipTriggerList = [].slice.call(document.querySelectorAll(\'[data-bs-toggle="tooltip"]\'));\n';
  js += '      var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {\n';
  js += '        return new bootstrap.Tooltip(tooltipTriggerEl);\n';
  js += '      });\n';
  js += '      \n';
  js += '      // Initialize popovers\n';
  js += '      var popoverTriggerList = [].slice.call(document.querySelectorAll(\'[data-bs-toggle="popover"]\'));\n';
  js += '      var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {\n';
  js += '        return new bootstrap.Popover(popoverTriggerEl);\n';
  js += '      });\n';
  js += '    });\n';
  js += '  </script>\n';
  
  return js;
}

// Utility functions
function escapeHTML(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function minifyHTML(html: string): string {
  return html
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .replace(/\s+>/g, '>')
    .replace(/<\s+/g, '<')
    .trim();
}

function minifyCSS(css: string): string {
  return css
    .replace(/\s+/g, ' ')
    .replace(/;\s*}/g, '}')
    .replace(/\s*{\s*/g, '{')
    .replace(/;\s*/g, ';')
    .replace(/,\s*/g, ',')
    .replace(/:\s*/g, ':')
    .trim();
}

// Export project as ZIP file
export async function exportProjectAsZip(project: Project, options: ExportOptions = defaultExportOptions): Promise<Blob> {
  // This would require a ZIP library like JSZip
  // For now, return a simple implementation
  const html = generateHTML(project, options);
  const css = options.separateCSS ? generateCSS(project, options) : '';
  
  // Create a simple text file with both HTML and CSS
  let content = html;
  if (css) {
    content += '\n\n/* styles.css */\n' + css;
  }
  
  return new Blob([content], { type: 'text/plain' });
}

// Download file helper
export function downloadFile(content: string, filename: string, mimeType: string = 'text/plain'): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
