{"name": "evolve-ui", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "tauri": "tauri"}, "license": "MIT", "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/prismjs": "^1.26.5", "@vitest/ui": "^3.2.4", "bootstrap": "^5.3.2", "jsdom": "^26.1.0", "prismjs": "^1.30.0", "shepherd.js": "^14.5.0", "vitest": "^3.2.4"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.3.5", "typescript": "~5.6.2", "vite": "^6.0.3"}}