# Evolve UI - Professional HTML Template Editor

A professional web-based HTML template editor built with SvelteKit, Bootstrap 5.x, and Tauri. Create responsive web layouts with an intuitive drag-and-drop interface, real-time preview, and comprehensive component library.

## 🚀 Features

### Core Architecture
- **SvelteKit + Tauri**: Modern web technologies with native desktop capabilities
- **Bootstrap 5.x Integration**: Comprehensive component library with full Bootstrap support
- **TypeScript**: Type-safe development with enhanced IDE support
- **Responsive Design**: Mobile-first approach with breakpoint support

### Professional Layout System
- **Three-Panel Layout**: Resizable and collapsible sidebars with CSS Grid/Flexbox
- **Component Library** (Left): Searchable Bootstrap components with thumbnails
- **Canvas Area** (Center): Visual editor with grid overlay and alignment guides
- **Properties Panel** (Right): Context-sensitive property editors with live preview

### Enhanced Component Library
- **Comprehensive Bootstrap Components**: 20+ components across 5 categories
  - Layout: Container, Row, Column, Flexbox utilities
  - Navigation: Navbar, Breadcrumb, Pagination
  - Forms: Input, Textarea, Select, Checkbox, Radio
  - Components: Button, Card, Alert, Modal
  - Content: Heading, Paragraph, Image, Table

- **Advanced Search & Filtering**:
  - Real-time search with 300ms debouncing
  - Category-based filtering with multi-select
  - Component thumbnails with hover previews
  - Drag & drop with visual feedback

### Professional Canvas Features
- **Dual Canvas Modes**:
  - Enhanced Canvas: Visual editor with grid and guides
  - Preview Mode: Real-time Bootstrap preview with Shadow DOM

- **Grid System**:
  - Bootstrap 12-column grid overlay with toggle
  - Snap-to-grid functionality (8px increments)
  - Visual grid indicators with major/minor lines

- **Alignment Guides**:
  - Smart alignment detection with 5px threshold
  - Edge, center, and component-to-component alignment
  - Visual feedback with colored guide lines

- **Component Interaction**:
  - Multi-select with Ctrl+click
  - Drag & drop with position constraints
  - Resize handles with aspect ratio lock
  - Copy/paste with keyboard shortcuts
  - Context menus with component actions

### Advanced Property Editors
- **Dimensions Editor**: Width/height with unit selection (px, %, rem, em)
- **Spacing Editor**: Visual margin/padding editor with Bootstrap utilities
- **Typography Editor**: Font family, size, weight, color with Google Fonts
- **Background Editor**: Color picker, gradient editor, image upload
- **Border Editor**: Style, width, color, radius with individual controls
- **Live CSS Viewer**: Syntax-highlighted CSS with Prism.js integration

### Command System & Shortcuts
- **Undo/Redo System**: Command pattern with 20-operation history
- **Keyboard Shortcuts**:
  - `Ctrl+Z/Y`: Undo/Redo
  - `Ctrl+C/V`: Copy/Paste
  - `Ctrl+K`: Command Palette
  - `Ctrl+S`: Manual Save
  - `Delete`: Remove Components
  - `Ctrl+B/I`: Toggle Sidebars

- **Command Palette**: Fuzzy search for quick actions with `Ctrl+K`

### Project Management
- **Auto-Save**: Every 30 seconds with localStorage persistence
- **Version History**: Last 10 versions with timestamps and previews
- **Project Export**:
  - Clean HTML with embedded CSS
  - Separate HTML/CSS files
  - Bootstrap CDN or local file options
  - Minification and optimization

### User Experience
- **Professional Theme System**: Light/dark mode with CSS custom properties
- **Responsive Interface**: Mobile, tablet, and desktop breakpoints
- **Accessibility**: WCAG 2.1 AA compliance with keyboard navigation
- **Performance**: Virtual scrolling, lazy loading, optimized rendering

## 🛠️ Technology Stack

- **Frontend**: SvelteKit, TypeScript, Bootstrap 5.x
- **Desktop**: Tauri (Rust)
- **Styling**: CSS Custom Properties, Tailwind CSS
- **Syntax Highlighting**: Prism.js
- **Testing**: Vitest with comprehensive test coverage
- **Build**: Vite with optimized bundling

## 📦 Installation

### Prerequisites
- Node.js 18+ and npm
- Rust (for Tauri desktop app)

### Development Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm run test

# Build for production
npm run build

# Build Tauri desktop app
npm run tauri build
```

## 🎯 Usage

### Getting Started
1. **Component Library**: Browse and search Bootstrap components in the left sidebar
2. **Canvas**: Drag components to the canvas area to start building
3. **Properties**: Select components to edit properties in the right panel
4. **Preview**: Toggle between Enhanced Canvas and Preview modes

### Keyboard Shortcuts
- `Ctrl+K`: Open command palette
- `Ctrl+Z/Y`: Undo/Redo actions
- `Ctrl+C/V`: Copy/paste components
- `Ctrl+S`: Save project
- `Ctrl+B`: Toggle component library
- `Ctrl+I`: Toggle properties panel
- `Ctrl+E`: Toggle canvas mode

### Project Export
1. Use the File menu or Command Palette
2. Choose export format (HTML, CSS, or combined)
3. Configure options (Bootstrap CDN, minification, etc.)
4. Download generated files

## 🧪 Testing

The project includes comprehensive test coverage:

```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui
```

Test coverage includes:
- Component store functionality
- Command system (undo/redo)
- Project management
- Keyboard shortcuts
- Export utilities

## 🏗️ Architecture

### Component Structure
```
src/lib/
├── components/          # Svelte components
│   ├── ComponentLibrary.svelte
│   ├── EnhancedCanvas.svelte
│   ├── PropertyPanel.svelte
│   ├── PropertyEditors.svelte
│   ├── CSSCodeViewer.svelte
│   ├── CommandPalette.svelte
│   ├── CanvasGrid.svelte
│   └── AlignmentGuides.svelte
├── stores/             # State management
│   ├── ComponentStore.ts
│   ├── ProjectStore.ts
│   ├── CommandStore.ts
│   ├── KeyboardStore.ts
│   └── ThemeStore.ts
└── utils/              # Utilities
    └── ExportUtils.ts
```

### State Management
- **Svelte Stores**: Reactive state management
- **Command Pattern**: Undo/redo functionality
- **Local Storage**: Project persistence
- **Session Storage**: Command history

## Recommended IDE Setup

[VS Code](https://code.visualstudio.com/) + [Svelte](https://marketplace.visualstudio.com/items?itemName=svelte.svelte-vscode) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔮 Future Enhancements

- **Component Nesting**: Hierarchical component structure
- **Custom Components**: User-defined component library
- **Collaboration**: Real-time collaborative editing
- **Templates**: Pre-built page templates
- **Animations**: CSS animation editor
- **Responsive Preview**: Multi-device preview modes
- **Plugin System**: Extensible architecture
- **Cloud Sync**: Project synchronization across devices

---

Built with ❤️ using SvelteKit, Bootstrap, and Tauri
